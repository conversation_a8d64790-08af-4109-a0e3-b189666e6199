
        <!DOCTYPE html>
        <html>
        <head>
            <title>GPS Rectangles Map</title>
            <meta charset="utf-8" />
            <style> #map { height: 100vh; width: 100%; } </style>
            <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
            <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
        </head>
        <body>
        <div id="map"></div>
        <script>
            var map = L.map('map').setView([41.407019, 2.168555], 18);
            <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 22,
                attribution: '&copy; OpenStreetMap contributors'
            }).addTo(map);

            L.polygon([[41.407019, 2.168555], [41.407070, 2.168623], [41.407005, 2.168709], [41.406903, 2.168574], [41.406968, 2.168487], [41.407019, 2.168555]], {color: 'red'}).addTo(map);
L.polygon([[41.406992, 2.168591], [41.406983, 2.168686], [41.406882, 2.168668], [41.406900, 2.168479], [41.407001, 2.168496], [41.406992, 2.168591]], {color: 'red'}).addTo(map);
L.polygon([[41.406848, 2.168566], [41.406772, 2.168668], [41.406673, 2.168537], [41.406825, 2.168333], [41.406924, 2.168464], [41.406848, 2.168566]], {color: 'red'}).addTo(map);
L.polygon([[41.406750, 2.168436], [41.406673, 2.168537], [41.406592, 2.168428], [41.406747, 2.168227], [41.406827, 2.168335], [41.406750, 2.168436]], {color: 'red'}).addTo(map);
L.polygon([[41.406669, 2.168326], [41.406592, 2.168426], [41.406510, 2.168315], [41.406664, 2.168114], [41.406746, 2.168226], [41.406669, 2.168326]], {color: 'red'}).addTo(map);
L.polygon([[41.406587, 2.168214], [41.406511, 2.168316], [41.406421, 2.168197], [41.406573, 2.167993], [41.406663, 2.168112], [41.406587, 2.168214]], {color: 'red'}).addTo(map);
L.polygon([[41.406497, 2.168095], [41.406420, 2.168196], [41.406354, 2.168107], [41.406508, 2.167905], [41.406574, 2.167994], [41.406497, 2.168095]], {color: 'red'}).addTo(map);
L.polygon([[41.406431, 2.168006], [41.406354, 2.168107], [41.406274, 2.168000], [41.406427, 2.167798], [41.406508, 2.167905], [41.406431, 2.168006]], {color: 'red'}).addTo(map);
L.polygon([[41.406351, 2.167899], [41.406275, 2.168001], [41.406178, 2.167873], [41.406331, 2.167670], [41.406427, 2.167797], [41.406351, 2.167899]], {color: 'red'}).addTo(map);
L.polygon([[41.406253, 2.167769], [41.406177, 2.167871], [41.406109, 2.167782], [41.406261, 2.167578], [41.406329, 2.167667], [41.406253, 2.167769]], {color: 'red'}).addTo(map);
L.polygon([[41.406187, 2.167682], [41.406110, 2.167783], [41.406031, 2.167677], [41.406184, 2.167475], [41.406264, 2.167581], [41.406187, 2.167682]], {color: 'red'}).addTo(map);
L.polygon([[41.406108, 2.167576], [41.406032, 2.167678], [41.405966, 2.167591], [41.406118, 2.167387], [41.406184, 2.167474], [41.406108, 2.167576]], {color: 'red'}).addTo(map);
L.polygon([[41.406042, 2.167489], [41.405966, 2.167591], [41.405890, 2.167490], [41.406042, 2.167287], [41.406118, 2.167387], [41.406042, 2.167489]], {color: 'red'}).addTo(map);
L.polygon([[41.405966, 2.167388], [41.405890, 2.167490], [41.405817, 2.167394], [41.405969, 2.167190], [41.406042, 2.167286], [41.405966, 2.167388]], {color: 'red'}).addTo(map);
L.polygon([[41.405893, 2.167292], [41.405817, 2.167394], [41.405727, 2.167276], [41.405880, 2.167072], [41.405969, 2.167190], [41.405893, 2.167292]], {color: 'red'}).addTo(map);
L.polygon([[41.405804, 2.167174], [41.405728, 2.167276], [41.405638, 2.167158], [41.405790, 2.166954], [41.405880, 2.167072], [41.405804, 2.167174]], {color: 'red'}).addTo(map);
L.polygon([[41.405714, 2.167055], [41.405638, 2.167157], [41.405541, 2.167030], [41.405694, 2.166826], [41.405790, 2.166953], [41.405714, 2.167055]], {color: 'red'}).addTo(map);
L.polygon([[41.405618, 2.166928], [41.405542, 2.167030], [41.405462, 2.166925], [41.405615, 2.166721], [41.405694, 2.166826], [41.405618, 2.166928]], {color: 'red'}).addTo(map);
L.polygon([[41.405539, 2.166823], [41.405463, 2.166925], [41.405380, 2.166816], [41.405531, 2.166611], [41.405615, 2.166721], [41.405539, 2.166823]], {color: 'red'}).addTo(map);
L.polygon([[41.405455, 2.166713], [41.405378, 2.166814], [41.405291, 2.166697], [41.405445, 2.166495], [41.405532, 2.166612], [41.405455, 2.166713]], {color: 'red'}).addTo(map);
L.polygon([[41.405368, 2.166596], [41.405445, 2.166495], [41.405532, 2.166612], [41.405378, 2.166814], [41.405291, 2.166697], [41.405368, 2.166596]], {color: 'red'}).addTo(map);
            L.marker([41.407019, 2.168555]).addTo(map).bindPopup(`<b>Point 0</b><br>Lat: 41.407019<br>Lon: 2.168555<br>Heading: 135.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.26 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.407019,2.168555&heading=135.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406992, 2.168591]).addTo(map).bindPopup(`<b>Point 1</b><br>Lat: 41.406992<br>Lon: 2.168591<br>Heading: 187.42<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.29 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406992,2.168591&heading=187.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406848, 2.168566]).addTo(map).bindPopup(`<b>Point 2</b><br>Lat: 41.406848<br>Lon: 2.168566<br>Heading: 224.85<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.52 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406848,2.168566&heading=224.85&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406750, 2.168436]).addTo(map).bindPopup(`<b>Point 3</b><br>Lat: 41.406750<br>Lon: 2.168436<br>Heading: 225.53<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.75 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406750,2.168436&heading=225.53&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406669, 2.168326]).addTo(map).bindPopup(`<b>Point 4</b><br>Lat: 41.406669<br>Lon: 2.168326<br>Heading: 225.69<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.02 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406669,2.168326&heading=225.69&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406587, 2.168214]).addTo(map).bindPopup(`<b>Point 5</b><br>Lat: 41.406587<br>Lon: 2.168214<br>Heading: 224.76<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.12 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406587,2.168214&heading=224.76&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406497, 2.168095]).addTo(map).bindPopup(`<b>Point 6</b><br>Lat: 41.406497<br>Lon: 2.168095<br>Heading: 225.33<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.45 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406497,2.168095&heading=225.33&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406431, 2.168006]).addTo(map).bindPopup(`<b>Point 7</b><br>Lat: 41.406431<br>Lon: 2.168006<br>Heading: 225.09<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.63 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406431,2.168006&heading=225.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406351, 2.167899]).addTo(map).bindPopup(`<b>Point 8</b><br>Lat: 41.406351<br>Lon: 2.167899<br>Heading: 224.85<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.12 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406351,2.167899&heading=224.85&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406253, 2.167769]).addTo(map).bindPopup(`<b>Point 9</b><br>Lat: 41.406253<br>Lon: 2.167769<br>Heading: 224.67<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.56 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406253,2.167769&heading=224.67&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406187, 2.167682]).addTo(map).bindPopup(`<b>Point 10</b><br>Lat: 41.406187<br>Lon: 2.167682<br>Heading: 225.18<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.48 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406187,2.167682&heading=225.18&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406108, 2.167576]).addTo(map).bindPopup(`<b>Point 11</b><br>Lat: 41.406108<br>Lon: 2.167576<br>Heading: 224.67<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.31 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406108,2.167576&heading=224.67&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406042, 2.167489]).addTo(map).bindPopup(`<b>Point 12</b><br>Lat: 41.406042<br>Lon: 2.167489<br>Heading: 224.91<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.91 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406042,2.167489&heading=224.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405966, 2.167388]).addTo(map).bindPopup(`<b>Point 13</b><br>Lat: 41.405966<br>Lon: 2.167388<br>Heading: 224.61<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.40 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405966,2.167388&heading=224.61&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405893, 2.167292]).addTo(map).bindPopup(`<b>Point 14</b><br>Lat: 41.405893<br>Lon: 2.167292<br>Heading: 224.84<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.01 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405893,2.167292&heading=224.84&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405804, 2.167174]).addTo(map).bindPopup(`<b>Point 15</b><br>Lat: 41.405804<br>Lon: 2.167174<br>Heading: 224.76<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.03 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405804,2.167174&heading=224.76&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405714, 2.167055]).addTo(map).bindPopup(`<b>Point 16</b><br>Lat: 41.405714<br>Lon: 2.167055<br>Heading: 224.78<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.09 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405714,2.167055&heading=224.78&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405618, 2.166928]).addTo(map).bindPopup(`<b>Point 17</b><br>Lat: 41.405618<br>Lon: 2.166928<br>Heading: 224.91<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.43 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405618,2.166928&heading=224.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405539, 2.166823]).addTo(map).bindPopup(`<b>Point 18</b><br>Lat: 41.405539<br>Lon: 2.166823<br>Heading: 224.49<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.03 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405539,2.166823&heading=224.49&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405455, 2.166713]).addTo(map).bindPopup(`<b>Point 19</b><br>Lat: 41.405455<br>Lon: 2.166713<br>Heading: 225.25<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.73 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405455,2.166713&heading=225.25&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405368, 2.166596]).addTo(map).bindPopup(`<b>Point 20</b><br>Lat: 41.405368<br>Lon: 2.166596<br>Heading: 45.25<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.73 m<br>Output Column: False m<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405368,2.166596&heading=45.25&pitch=0&fov=80' target='_blank'>Street View</a>`);
        </script>
        </body>
        </html>
        