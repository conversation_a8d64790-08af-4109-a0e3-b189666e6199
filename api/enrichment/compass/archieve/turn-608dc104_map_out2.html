
        <!DOCTYPE html>
        <html>
        <head>
            <title>GPS Rectangles Map</title>
            <meta charset="utf-8" />
            <style> #map { height: 100vh; width: 100%; } </style>
            <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
            <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
        </head>
        <body>
        <div id="map"></div>
        <script>
            var map = L.map('map').setView([41.407019, 2.168555], 18);
            <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 22,
                attribution: '&copy; OpenStreetMap contributors'
            }).addTo(map);

            L.polygon([[41.407019, 2.168555], [41.407070, 2.168623], [41.407005, 2.168709], [41.406903, 2.168574], [41.406968, 2.168487], [41.407019, 2.168555]], {color: 'red'}).addTo(map);
L.polygon([[41.406992, 2.168591], [41.406983, 2.168686], [41.406882, 2.168668], [41.406900, 2.168479], [41.407001, 2.168496], [41.406992, 2.168591]], {color: 'red'}).addTo(map);
L.polygon([[41.406848, 2.168566], [41.406772, 2.168668], [41.406673, 2.168537], [41.406825, 2.168333], [41.406924, 2.168464], [41.406848, 2.168566]], {color: 'red'}).addTo(map);
L.polygon([[41.406750, 2.168436], [41.406673, 2.168537], [41.406592, 2.168428], [41.406747, 2.168227], [41.406827, 2.168335], [41.406750, 2.168436]], {color: 'red'}).addTo(map);
L.polygon([[41.406669, 2.168326], [41.406592, 2.168426], [41.406510, 2.168315], [41.406664, 2.168114], [41.406746, 2.168226], [41.406669, 2.168326]], {color: 'red'}).addTo(map);
L.polygon([[41.406587, 2.168214], [41.406511, 2.168316], [41.406421, 2.168197], [41.406573, 2.167993], [41.406663, 2.168112], [41.406587, 2.168214]], {color: 'red'}).addTo(map);
L.polygon([[41.406497, 2.168095], [41.406420, 2.168196], [41.406354, 2.168107], [41.406508, 2.167905], [41.406574, 2.167994], [41.406497, 2.168095]], {color: 'red'}).addTo(map);
L.polygon([[41.406431, 2.168006], [41.406354, 2.168107], [41.406274, 2.168000], [41.406427, 2.167798], [41.406508, 2.167905], [41.406431, 2.168006]], {color: 'red'}).addTo(map);
L.polygon([[41.406351, 2.167899], [41.406275, 2.168001], [41.406178, 2.167873], [41.406331, 2.167670], [41.406427, 2.167797], [41.406351, 2.167899]], {color: 'red'}).addTo(map);
L.polygon([[41.406253, 2.167769], [41.406177, 2.167871], [41.406109, 2.167782], [41.406261, 2.167578], [41.406329, 2.167667], [41.406253, 2.167769]], {color: 'red'}).addTo(map);
L.polygon([[41.406187, 2.167682], [41.406110, 2.167783], [41.406031, 2.167677], [41.406184, 2.167475], [41.406264, 2.167581], [41.406187, 2.167682]], {color: 'red'}).addTo(map);
L.polygon([[41.406108, 2.167576], [41.406032, 2.167678], [41.405966, 2.167591], [41.406118, 2.167387], [41.406184, 2.167474], [41.406108, 2.167576]], {color: 'red'}).addTo(map);
L.polygon([[41.406042, 2.167489], [41.405966, 2.167591], [41.405890, 2.167490], [41.406042, 2.167287], [41.406118, 2.167387], [41.406042, 2.167489]], {color: 'red'}).addTo(map);
L.polygon([[41.405966, 2.167388], [41.405890, 2.167490], [41.405817, 2.167394], [41.405969, 2.167190], [41.406042, 2.167286], [41.405966, 2.167388]], {color: 'red'}).addTo(map);
L.polygon([[41.405893, 2.167292], [41.405817, 2.167394], [41.405727, 2.167276], [41.405880, 2.167072], [41.405969, 2.167190], [41.405893, 2.167292]], {color: 'red'}).addTo(map);
L.polygon([[41.405804, 2.167174], [41.405728, 2.167276], [41.405638, 2.167158], [41.405790, 2.166954], [41.405880, 2.167072], [41.405804, 2.167174]], {color: 'red'}).addTo(map);
L.polygon([[41.405714, 2.167055], [41.405638, 2.167157], [41.405541, 2.167030], [41.405694, 2.166826], [41.405790, 2.166953], [41.405714, 2.167055]], {color: 'red'}).addTo(map);
L.polygon([[41.405618, 2.166928], [41.405542, 2.167030], [41.405462, 2.166925], [41.405615, 2.166721], [41.405694, 2.166826], [41.405618, 2.166928]], {color: 'red'}).addTo(map);
L.polygon([[41.405539, 2.166823], [41.405463, 2.166925], [41.405380, 2.166816], [41.405531, 2.166611], [41.405615, 2.166721], [41.405539, 2.166823]], {color: 'red'}).addTo(map);
L.polygon([[41.405455, 2.166713], [41.405378, 2.166814], [41.405291, 2.166697], [41.405445, 2.166495], [41.405532, 2.166612], [41.405455, 2.166713]], {color: 'red'}).addTo(map);
L.polygon([[41.405368, 2.166596], [41.405292, 2.166698], [41.405195, 2.166570], [41.405348, 2.166367], [41.405444, 2.166494], [41.405368, 2.166596]], {color: 'red'}).addTo(map);
L.polygon([[41.405220, 2.166399], [41.405145, 2.166502], [41.405042, 2.166368], [41.405193, 2.166163], [41.405295, 2.166296], [41.405220, 2.166399]], {color: 'red'}).addTo(map);
L.polygon([[41.405117, 2.166265], [41.405041, 2.166367], [41.404940, 2.166234], [41.405091, 2.166030], [41.405193, 2.166163], [41.405117, 2.166265]], {color: 'red'}).addTo(map);
L.polygon([[41.405016, 2.166132], [41.404940, 2.166234], [41.404846, 2.166110], [41.404998, 2.165906], [41.405092, 2.166030], [41.405016, 2.166132]], {color: 'red'}).addTo(map);
L.polygon([[41.404922, 2.166008], [41.404848, 2.166112], [41.404742, 2.165979], [41.404890, 2.165770], [41.404996, 2.165904], [41.404922, 2.166008]], {color: 'red'}).addTo(map);
L.polygon([[41.404816, 2.165874], [41.404741, 2.165977], [41.404638, 2.165846], [41.404788, 2.165639], [41.404891, 2.165771], [41.404816, 2.165874]], {color: 'red'}).addTo(map);
L.polygon([[41.404714, 2.165743], [41.404638, 2.165845], [41.404536, 2.165711], [41.404687, 2.165507], [41.404790, 2.165641], [41.404714, 2.165743]], {color: 'red'}).addTo(map);
L.polygon([[41.404611, 2.165608], [41.404535, 2.165710], [41.404446, 2.165593], [41.404599, 2.165390], [41.404687, 2.165506], [41.404611, 2.165608]], {color: 'red'}).addTo(map);
L.polygon([[41.404523, 2.165491], [41.404447, 2.165593], [41.404349, 2.165465], [41.404501, 2.165261], [41.404599, 2.165389], [41.404523, 2.165491]], {color: 'red'}).addTo(map);
L.polygon([[41.404426, 2.165363], [41.404349, 2.165463], [41.404250, 2.165329], [41.404405, 2.165128], [41.404503, 2.165263], [41.404426, 2.165363]], {color: 'red'}).addTo(map);
L.polygon([[41.404328, 2.165229], [41.404250, 2.165328], [41.404150, 2.165190], [41.404306, 2.164991], [41.404406, 2.165130], [41.404328, 2.165229]], {color: 'red'}).addTo(map);
L.polygon([[41.404228, 2.165090], [41.404149, 2.165189], [41.404049, 2.165048], [41.404206, 2.164851], [41.404307, 2.164991], [41.404228, 2.165090]], {color: 'red'}).addTo(map);
L.polygon([[41.404128, 2.164949], [41.404050, 2.165048], [41.403962, 2.164925], [41.404118, 2.164727], [41.404206, 2.164850], [41.404128, 2.164949]], {color: 'red'}).addTo(map);
L.polygon([[41.404040, 2.164826], [41.403962, 2.164925], [41.403898, 2.164836], [41.404054, 2.164638], [41.404118, 2.164727], [41.404040, 2.164826]], {color: 'red'}).addTo(map);
L.polygon([[41.403976, 2.164737], [41.403899, 2.164838], [41.403834, 2.164750], [41.403988, 2.164549], [41.404053, 2.164636], [41.403976, 2.164737]], {color: 'red'}).addTo(map);
L.polygon([[41.403911, 2.164649], [41.403835, 2.164751], [41.403754, 2.164645], [41.403906, 2.164441], [41.403987, 2.164547], [41.403911, 2.164649]], {color: 'red'}).addTo(map);
L.polygon([[41.403830, 2.164542], [41.403753, 2.164643], [41.403661, 2.164518], [41.403815, 2.164317], [41.403907, 2.164441], [41.403830, 2.164542]], {color: 'red'}).addTo(map);
L.polygon([[41.403738, 2.164417], [41.403661, 2.164518], [41.403578, 2.164407], [41.403731, 2.164204], [41.403815, 2.164316], [41.403738, 2.164417]], {color: 'red'}).addTo(map);
L.polygon([[41.403655, 2.164306], [41.403580, 2.164409], [41.403490, 2.164294], [41.403641, 2.164088], [41.403730, 2.164203], [41.403655, 2.164306]], {color: 'red'}).addTo(map);
L.polygon([[41.403565, 2.164190], [41.403488, 2.164291], [41.403413, 2.164190], [41.403566, 2.163988], [41.403642, 2.164089], [41.403565, 2.164190]], {color: 'red'}).addTo(map);
L.polygon([[41.403495, 2.164096], [41.403505, 2.164239], [41.403409, 2.164251], [41.403389, 2.163965], [41.403485, 2.163953], [41.403495, 2.164096]], {color: 'red'}).addTo(map);
L.polygon([[41.403278, 2.164123], [41.403303, 2.164158], [41.403211, 2.164274], [41.403161, 2.164205], [41.403253, 2.164088], [41.403278, 2.164123]], {color: 'red'}).addTo(map);
L.polygon([[41.403183, 2.164244], [41.403208, 2.164278], [41.403124, 2.164389], [41.403074, 2.164321], [41.403158, 2.164210], [41.403183, 2.164244]], {color: 'red'}).addTo(map);
L.polygon([[41.403099, 2.164355], [41.403124, 2.164389], [41.403041, 2.164498], [41.402991, 2.164430], [41.403074, 2.164321], [41.403099, 2.164355]], {color: 'red'}).addTo(map);
L.polygon([[41.403016, 2.164464], [41.403042, 2.164498], [41.402961, 2.164605], [41.402910, 2.164537], [41.402990, 2.164430], [41.403016, 2.164464]], {color: 'red'}).addTo(map);
L.polygon([[41.402936, 2.164571], [41.402961, 2.164605], [41.402878, 2.164715], [41.402827, 2.164647], [41.402911, 2.164537], [41.402936, 2.164571]], {color: 'red'}).addTo(map);
L.polygon([[41.402853, 2.164681], [41.402878, 2.164715], [41.402810, 2.164806], [41.402759, 2.164738], [41.402828, 2.164647], [41.402853, 2.164681]], {color: 'red'}).addTo(map);
L.polygon([[41.402784, 2.164773], [41.402809, 2.164807], [41.402717, 2.164928], [41.402667, 2.164860], [41.402759, 2.164739], [41.402784, 2.164773]], {color: 'red'}).addTo(map);
L.polygon([[41.402694, 2.164892], [41.402719, 2.164927], [41.402637, 2.165030], [41.402587, 2.164961], [41.402669, 2.164857], [41.402694, 2.164892]], {color: 'red'}).addTo(map);
L.polygon([[41.402612, 2.164996], [41.402637, 2.165031], [41.402529, 2.165167], [41.402479, 2.165098], [41.402587, 2.164961], [41.402612, 2.164996]], {color: 'red'}).addTo(map);
L.polygon([[41.402503, 2.165134], [41.402528, 2.165168], [41.402417, 2.165315], [41.402367, 2.165248], [41.402478, 2.165100], [41.402503, 2.165134]], {color: 'red'}).addTo(map);
L.polygon([[41.402392, 2.165282], [41.402417, 2.165316], [41.402302, 2.165467], [41.402252, 2.165399], [41.402367, 2.165248], [41.402392, 2.165282]], {color: 'red'}).addTo(map);
L.polygon([[41.402277, 2.165434], [41.402302, 2.165468], [41.402185, 2.165624], [41.402134, 2.165556], [41.402252, 2.165400], [41.402277, 2.165434]], {color: 'red'}).addTo(map);
L.polygon([[41.402160, 2.165590], [41.402186, 2.165624], [41.402078, 2.165767], [41.402027, 2.165700], [41.402134, 2.165556], [41.402160, 2.165590]], {color: 'red'}).addTo(map);
L.polygon([[41.402053, 2.165734], [41.402078, 2.165768], [41.401985, 2.165891], [41.401934, 2.165823], [41.402028, 2.165700], [41.402053, 2.165734]], {color: 'red'}).addTo(map);
L.polygon([[41.401960, 2.165857], [41.401985, 2.165891], [41.401913, 2.165985], [41.401863, 2.165916], [41.401935, 2.165823], [41.401960, 2.165857]], {color: 'red'}).addTo(map);
L.polygon([[41.401888, 2.165951], [41.401913, 2.165985], [41.401819, 2.166108], [41.401769, 2.166040], [41.401863, 2.165917], [41.401888, 2.165951]], {color: 'red'}).addTo(map);
L.polygon([[41.401794, 2.166075], [41.401819, 2.166109], [41.401726, 2.166231], [41.401676, 2.166162], [41.401769, 2.166041], [41.401794, 2.166075]], {color: 'red'}).addTo(map);
L.polygon([[41.401711, 2.166184], [41.401637, 2.166289], [41.401556, 2.166189], [41.401703, 2.165979], [41.401785, 2.166079], [41.401711, 2.166184]], {color: 'red'}).addTo(map);
L.polygon([[41.401636, 2.166091], [41.401560, 2.166193], [41.401485, 2.166095], [41.401636, 2.165890], [41.401712, 2.165989], [41.401636, 2.166091]], {color: 'red'}).addTo(map);
L.polygon([[41.401561, 2.165993], [41.401485, 2.166095], [41.401421, 2.166010], [41.401573, 2.165807], [41.401637, 2.165891], [41.401561, 2.165993]], {color: 'red'}).addTo(map);
L.polygon([[41.401497, 2.165908], [41.401422, 2.166011], [41.401355, 2.165925], [41.401505, 2.165718], [41.401572, 2.165805], [41.401497, 2.165908]], {color: 'red'}).addTo(map);
L.polygon([[41.401430, 2.165822], [41.401355, 2.165925], [41.401284, 2.165834], [41.401435, 2.165628], [41.401505, 2.165719], [41.401430, 2.165822]], {color: 'red'}).addTo(map);
L.polygon([[41.401360, 2.165731], [41.401285, 2.165834], [41.401200, 2.165725], [41.401351, 2.165519], [41.401435, 2.165628], [41.401360, 2.165731]], {color: 'red'}).addTo(map);
L.polygon([[41.401275, 2.165621], [41.401200, 2.165724], [41.401117, 2.165616], [41.401267, 2.165411], [41.401350, 2.165518], [41.401275, 2.165621]], {color: 'red'}).addTo(map);
L.polygon([[41.401192, 2.165513], [41.401116, 2.165615], [41.401020, 2.165488], [41.401172, 2.165285], [41.401268, 2.165411], [41.401192, 2.165513]], {color: 'red'}).addTo(map);
L.polygon([[41.401097, 2.165387], [41.401021, 2.165489], [41.400906, 2.165336], [41.401058, 2.165133], [41.401173, 2.165285], [41.401097, 2.165387]], {color: 'red'}).addTo(map);
L.polygon([[41.400982, 2.165234], [41.400909, 2.165339], [41.400789, 2.165193], [41.400936, 2.164982], [41.401055, 2.165129], [41.400982, 2.165234]], {color: 'red'}).addTo(map);
L.polygon([[41.400863, 2.165087], [41.400790, 2.165192], [41.400692, 2.165072], [41.400839, 2.164862], [41.400936, 2.164982], [41.400863, 2.165087]], {color: 'red'}).addTo(map);
L.polygon([[41.400766, 2.164967], [41.400692, 2.165072], [41.400603, 2.164961], [41.400751, 2.164752], [41.400840, 2.164862], [41.400766, 2.164967]], {color: 'red'}).addTo(map);
L.polygon([[41.400677, 2.164856], [41.400603, 2.164961], [41.400529, 2.164868], [41.400676, 2.164658], [41.400751, 2.164751], [41.400677, 2.164856]], {color: 'red'}).addTo(map);
L.polygon([[41.400566, 2.164718], [41.400490, 2.164820], [41.400417, 2.164725], [41.400569, 2.164520], [41.400642, 2.164616], [41.400566, 2.164718]], {color: 'red'}).addTo(map);
L.polygon([[41.400492, 2.164621], [41.400415, 2.164722], [41.400346, 2.164629], [41.400500, 2.164427], [41.400569, 2.164520], [41.400492, 2.164621]], {color: 'red'}).addTo(map);
L.polygon([[41.400423, 2.164528], [41.400348, 2.164631], [41.400277, 2.164540], [41.400428, 2.164334], [41.400498, 2.164425], [41.400423, 2.164528]], {color: 'red'}).addTo(map);
L.polygon([[41.400353, 2.164437], [41.400277, 2.164539], [41.400178, 2.164408], [41.400330, 2.164205], [41.400429, 2.164335], [41.400353, 2.164437]], {color: 'red'}).addTo(map);
L.polygon([[41.400254, 2.164306], [41.400177, 2.164407], [41.400095, 2.164297], [41.400248, 2.164095], [41.400331, 2.164205], [41.400254, 2.164306]], {color: 'red'}).addTo(map);
L.polygon([[41.400172, 2.164196], [41.400096, 2.164298], [41.400007, 2.164180], [41.400159, 2.163977], [41.400248, 2.164094], [41.400172, 2.164196]], {color: 'red'}).addTo(map);
L.polygon([[41.400083, 2.164078], [41.400005, 2.164177], [41.399907, 2.164042], [41.400063, 2.163843], [41.400161, 2.163979], [41.400083, 2.164078]], {color: 'red'}).addTo(map);
L.polygon([[41.399986, 2.163943], [41.399909, 2.164043], [41.399785, 2.163876], [41.399940, 2.163675], [41.400063, 2.163843], [41.399986, 2.163943]], {color: 'red'}).addTo(map);
L.polygon([[41.399862, 2.163774], [41.399785, 2.163874], [41.399719, 2.163785], [41.399874, 2.163585], [41.399939, 2.163674], [41.399862, 2.163774]], {color: 'red'}).addTo(map);
L.polygon([[41.399797, 2.163685], [41.399719, 2.163785], [41.399646, 2.163684], [41.399802, 2.163484], [41.399875, 2.163585], [41.399797, 2.163685]], {color: 'red'}).addTo(map);
L.polygon([[41.399724, 2.163584], [41.399647, 2.163684], [41.399584, 2.163599], [41.399738, 2.163398], [41.399801, 2.163484], [41.399724, 2.163584]], {color: 'red'}).addTo(map);
L.polygon([[41.399661, 2.163498], [41.399584, 2.163598], [41.399504, 2.163490], [41.399659, 2.163290], [41.399738, 2.163398], [41.399661, 2.163498]], {color: 'red'}).addTo(map);
L.polygon([[41.399582, 2.163390], [41.399504, 2.163490], [41.399439, 2.163400], [41.399595, 2.163201], [41.399660, 2.163290], [41.399582, 2.163390]], {color: 'red'}).addTo(map);
L.polygon([[41.399517, 2.163300], [41.399441, 2.163401], [41.399364, 2.163300], [41.399517, 2.163097], [41.399593, 2.163199], [41.399517, 2.163300]], {color: 'red'}).addTo(map);
L.polygon([[41.399442, 2.163200], [41.399369, 2.163306], [41.399302, 2.163224], [41.399449, 2.163013], [41.399515, 2.163094], [41.399442, 2.163200]], {color: 'red'}).addTo(map);
L.polygon([[41.399376, 2.163119], [41.399304, 2.163225], [41.399230, 2.163138], [41.399375, 2.162925], [41.399448, 2.163013], [41.399376, 2.163119]], {color: 'red'}).addTo(map);
L.polygon([[41.399303, 2.163031], [41.399228, 2.163134], [41.399101, 2.162971], [41.399251, 2.162765], [41.399378, 2.162928], [41.399303, 2.163031]], {color: 'red'}).addTo(map);
L.polygon([[41.399175, 2.162866], [41.399098, 2.162967], [41.398999, 2.162833], [41.399153, 2.162632], [41.399252, 2.162765], [41.399175, 2.162866]], {color: 'red'}).addTo(map);
L.polygon([[41.399073, 2.162728], [41.398995, 2.162828], [41.398909, 2.162710], [41.399064, 2.162510], [41.399151, 2.162628], [41.399073, 2.162728]], {color: 'red'}).addTo(map);
L.polygon([[41.398990, 2.162614], [41.398913, 2.162714], [41.398847, 2.162625], [41.399002, 2.162425], [41.399067, 2.162514], [41.398990, 2.162614]], {color: 'red'}).addTo(map);
L.polygon([[41.398925, 2.162525], [41.398847, 2.162625], [41.398764, 2.162510], [41.398919, 2.162310], [41.399003, 2.162425], [41.398925, 2.162525]], {color: 'red'}).addTo(map);
L.polygon([[41.398837, 2.162404], [41.398760, 2.162505], [41.398677, 2.162394], [41.398831, 2.162192], [41.398914, 2.162303], [41.398837, 2.162404]], {color: 'red'}).addTo(map);
L.polygon([[41.398759, 2.162299], [41.398683, 2.162401], [41.398607, 2.162300], [41.398760, 2.162097], [41.398835, 2.162197], [41.398759, 2.162299]], {color: 'red'}).addTo(map);
L.polygon([[41.398683, 2.162198], [41.398606, 2.162299], [41.398526, 2.162190], [41.398679, 2.161989], [41.398760, 2.162097], [41.398683, 2.162198]], {color: 'red'}).addTo(map);
L.polygon([[41.398603, 2.162090], [41.398528, 2.162194], [41.398460, 2.162107], [41.398610, 2.161900], [41.398678, 2.161986], [41.398603, 2.162090]], {color: 'red'}).addTo(map);
L.polygon([[41.398535, 2.162003], [41.398461, 2.162108], [41.398380, 2.162006], [41.398527, 2.161797], [41.398609, 2.161898], [41.398535, 2.162003]], {color: 'red'}).addTo(map);
L.polygon([[41.398454, 2.161902], [41.398380, 2.162007], [41.398306, 2.161915], [41.398453, 2.161705], [41.398528, 2.161797], [41.398454, 2.161902]], {color: 'red'}).addTo(map);
L.polygon([[41.398379, 2.161809], [41.398304, 2.161912], [41.398230, 2.161817], [41.398381, 2.161611], [41.398454, 2.161706], [41.398379, 2.161809]], {color: 'red'}).addTo(map);
L.polygon([[41.398305, 2.161713], [41.398228, 2.161814], [41.398163, 2.161726], [41.398316, 2.161524], [41.398382, 2.161612], [41.398305, 2.161713]], {color: 'red'}).addTo(map);
L.polygon([[41.398240, 2.161626], [41.398164, 2.161727], [41.398048, 2.161573], [41.398201, 2.161371], [41.398316, 2.161525], [41.398240, 2.161626]], {color: 'red'}).addTo(map);
L.polygon([[41.398124, 2.161471], [41.398047, 2.161572], [41.397965, 2.161463], [41.398119, 2.161260], [41.398201, 2.161370], [41.398124, 2.161471]], {color: 'red'}).addTo(map);
L.polygon([[41.398042, 2.161361], [41.397965, 2.161462], [41.397886, 2.161356], [41.398039, 2.161154], [41.398119, 2.161260], [41.398042, 2.161361]], {color: 'red'}).addTo(map);
L.polygon([[41.397963, 2.161255], [41.397887, 2.161357], [41.397820, 2.161269], [41.397972, 2.161065], [41.398039, 2.161153], [41.397963, 2.161255]], {color: 'red'}).addTo(map);
L.polygon([[41.397896, 2.161167], [41.397821, 2.161270], [41.397733, 2.161157], [41.397884, 2.160951], [41.397971, 2.161064], [41.397896, 2.161167]], {color: 'red'}).addTo(map);
L.polygon([[41.397809, 2.161054], [41.397733, 2.161156], [41.397657, 2.161056], [41.397808, 2.160851], [41.397885, 2.160952], [41.397809, 2.161054]], {color: 'red'}).addTo(map);
L.polygon([[41.397733, 2.160954], [41.397658, 2.161057], [41.397587, 2.160966], [41.397737, 2.160760], [41.397808, 2.160851], [41.397733, 2.160954]], {color: 'red'}).addTo(map);
L.polygon([[41.397664, 2.160865], [41.397588, 2.160968], [41.397519, 2.160878], [41.397670, 2.160673], [41.397740, 2.160762], [41.397664, 2.160865]], {color: 'red'}).addTo(map);
L.polygon([[41.397628, 2.160818], [41.397660, 2.160955], [41.397561, 2.160996], [41.397496, 2.160722], [41.397596, 2.160681], [41.397628, 2.160818]], {color: 'red'}).addTo(map);
L.polygon([[41.397419, 2.160905], [41.397522, 2.161039], [41.397457, 2.161126], [41.397252, 2.160858], [41.397316, 2.160771], [41.397419, 2.160905]], {color: 'red'}).addTo(map);
L.polygon([[41.397360, 2.160985], [41.397461, 2.161121], [41.397390, 2.161214], [41.397188, 2.160941], [41.397259, 2.160849], [41.397360, 2.160985]], {color: 'red'}).addTo(map);
L.polygon([[41.397289, 2.161078], [41.397391, 2.161213], [41.397322, 2.161305], [41.397118, 2.161035], [41.397187, 2.160943], [41.397289, 2.161078]], {color: 'red'}).addTo(map);
L.polygon([[41.397220, 2.161170], [41.397320, 2.161308], [41.397251, 2.161396], [41.397051, 2.161121], [41.397120, 2.161032], [41.397220, 2.161170]], {color: 'red'}).addTo(map);
L.polygon([[41.397153, 2.161256], [41.397251, 2.161396], [41.397182, 2.161481], [41.396986, 2.161201], [41.397055, 2.161116], [41.397153, 2.161256]], {color: 'red'}).addTo(map);
L.polygon([[41.397086, 2.161339], [41.397097, 2.161530], [41.397004, 2.161539], [41.396983, 2.161157], [41.397075, 2.161148], [41.397086, 2.161339]], {color: 'red'}).addTo(map);
L.polygon([[41.396994, 2.161348], [41.396891, 2.161393], [41.396857, 2.161257], [41.397062, 2.161166], [41.397097, 2.161303], [41.396994, 2.161348]], {color: 'red'}).addTo(map);
L.polygon([[41.396960, 2.161212], [41.396857, 2.161257], [41.396819, 2.161102], [41.397025, 2.161013], [41.397063, 2.161167], [41.396960, 2.161212]], {color: 'red'}).addTo(map);
L.polygon([[41.396922, 2.161057], [41.396820, 2.161103], [41.396777, 2.160934], [41.396982, 2.160843], [41.397024, 2.161011], [41.396922, 2.161057]], {color: 'red'}).addTo(map);
L.polygon([[41.396880, 2.160890], [41.396777, 2.160934], [41.396748, 2.160814], [41.396954, 2.160726], [41.396983, 2.160846], [41.396880, 2.160890]], {color: 'red'}).addTo(map);
L.polygon([[41.396851, 2.160770], [41.396749, 2.160816], [41.396714, 2.160681], [41.396919, 2.160589], [41.396953, 2.160724], [41.396851, 2.160770]], {color: 'red'}).addTo(map);
L.polygon([[41.396817, 2.160636], [41.396715, 2.160683], [41.396680, 2.160548], [41.396884, 2.160454], [41.396919, 2.160589], [41.396817, 2.160636]], {color: 'red'}).addTo(map);
L.polygon([[41.396778, 2.160487], [41.396676, 2.160536], [41.396643, 2.160415], [41.396847, 2.160317], [41.396880, 2.160438], [41.396778, 2.160487]], {color: 'red'}).addTo(map);
L.polygon([[41.396745, 2.160365], [41.396639, 2.160394], [41.396616, 2.160247], [41.396827, 2.160188], [41.396851, 2.160336], [41.396745, 2.160365]], {color: 'red'}).addTo(map);
L.polygon([[41.396719, 2.160200], [41.396613, 2.160225], [41.396589, 2.160047], [41.396802, 2.159997], [41.396825, 2.160175], [41.396719, 2.160200]], {color: 'red'}).addTo(map);
L.polygon([[41.396695, 2.160018], [41.396589, 2.160043], [41.396565, 2.159864], [41.396778, 2.159815], [41.396801, 2.159993], [41.396695, 2.160018]], {color: 'red'}).addTo(map);
L.polygon([[41.396672, 2.159842], [41.396565, 2.159860], [41.396546, 2.159663], [41.396761, 2.159627], [41.396779, 2.159824], [41.396672, 2.159842]], {color: 'red'}).addTo(map);
L.polygon([[41.396653, 2.159638], [41.396552, 2.159689], [41.396500, 2.159508], [41.396702, 2.159406], [41.396754, 2.159587], [41.396653, 2.159638]], {color: 'red'}).addTo(map);
L.polygon([[41.396607, 2.159478], [41.396506, 2.159528], [41.396454, 2.159343], [41.396656, 2.159243], [41.396708, 2.159428], [41.396607, 2.159478]], {color: 'red'}).addTo(map);
L.polygon([[41.396556, 2.159296], [41.396465, 2.159373], [41.396412, 2.159264], [41.396595, 2.159109], [41.396647, 2.159219], [41.396556, 2.159296]], {color: 'red'}).addTo(map);
L.polygon([[41.396512, 2.159204], [41.396430, 2.159298], [41.396350, 2.159174], [41.396513, 2.158987], [41.396594, 2.159110], [41.396512, 2.159204]], {color: 'red'}).addTo(map);
L.polygon([[41.396416, 2.159056], [41.396313, 2.159101], [41.396281, 2.158972], [41.396486, 2.158882], [41.396519, 2.159011], [41.396416, 2.159056]], {color: 'red'}).addTo(map);
L.polygon([[41.396384, 2.158927], [41.396282, 2.158973], [41.396223, 2.158742], [41.396428, 2.158650], [41.396486, 2.158881], [41.396384, 2.158927]], {color: 'red'}).addTo(map);
L.polygon([[41.396326, 2.158696], [41.396223, 2.158741], [41.396192, 2.158616], [41.396398, 2.158526], [41.396429, 2.158651], [41.396326, 2.158696]], {color: 'red'}).addTo(map);
L.polygon([[41.396295, 2.158571], [41.396192, 2.158615], [41.396162, 2.158493], [41.396368, 2.158404], [41.396398, 2.158527], [41.396295, 2.158571]], {color: 'red'}).addTo(map);
L.polygon([[41.396265, 2.158448], [41.396162, 2.158493], [41.396130, 2.158365], [41.396335, 2.158274], [41.396368, 2.158403], [41.396265, 2.158448]], {color: 'red'}).addTo(map);
L.polygon([[41.396233, 2.158320], [41.396130, 2.158365], [41.396077, 2.158150], [41.396282, 2.158059], [41.396336, 2.158275], [41.396233, 2.158320]], {color: 'red'}).addTo(map);
L.polygon([[41.396179, 2.158103], [41.396076, 2.158148], [41.396031, 2.157968], [41.396236, 2.157877], [41.396282, 2.158058], [41.396179, 2.158103]], {color: 'red'}).addTo(map);
L.polygon([[41.396134, 2.157923], [41.396032, 2.157969], [41.395987, 2.157791], [41.396192, 2.157700], [41.396236, 2.157877], [41.396134, 2.157923]], {color: 'red'}).addTo(map);
L.polygon([[41.396089, 2.157744], [41.395986, 2.157789], [41.395945, 2.157622], [41.396151, 2.157532], [41.396192, 2.157699], [41.396089, 2.157744]], {color: 'red'}).addTo(map);
L.polygon([[41.396048, 2.157577], [41.395945, 2.157622], [41.395902, 2.157450], [41.396107, 2.157359], [41.396151, 2.157532], [41.396048, 2.157577]], {color: 'red'}).addTo(map);
L.polygon([[41.396005, 2.157404], [41.395903, 2.157450], [41.395873, 2.157335], [41.396078, 2.157243], [41.396107, 2.157358], [41.396005, 2.157404]], {color: 'red'}).addTo(map);
L.polygon([[41.395976, 2.157289], [41.395874, 2.157335], [41.395833, 2.157172], [41.396037, 2.157081], [41.396078, 2.157243], [41.395976, 2.157289]], {color: 'red'}).addTo(map);
L.polygon([[41.395935, 2.157126], [41.395832, 2.157171], [41.395799, 2.157037], [41.396005, 2.156948], [41.396038, 2.157081], [41.395935, 2.157126]], {color: 'red'}).addTo(map);
L.polygon([[41.395902, 2.156992], [41.395800, 2.157038], [41.395759, 2.156879], [41.395964, 2.156787], [41.396004, 2.156946], [41.395902, 2.156992]], {color: 'red'}).addTo(map);
L.polygon([[41.395862, 2.156833], [41.395760, 2.156879], [41.395721, 2.156728], [41.395925, 2.156635], [41.395964, 2.156787], [41.395862, 2.156833]], {color: 'red'}).addTo(map);
L.polygon([[41.395823, 2.156681], [41.395721, 2.156728], [41.395677, 2.156559], [41.395881, 2.156465], [41.395925, 2.156634], [41.395823, 2.156681]], {color: 'red'}).addTo(map);
L.polygon([[41.395779, 2.156512], [41.395677, 2.156560], [41.395633, 2.156393], [41.395837, 2.156298], [41.395881, 2.156464], [41.395779, 2.156512]], {color: 'red'}).addTo(map);
L.polygon([[41.395735, 2.156345], [41.395633, 2.156391], [41.395580, 2.156184], [41.395784, 2.156092], [41.395837, 2.156299], [41.395735, 2.156345]], {color: 'red'}).addTo(map);
L.polygon([[41.395682, 2.156138], [41.395580, 2.156185], [41.395546, 2.156053], [41.395750, 2.155960], [41.395784, 2.156091], [41.395682, 2.156138]], {color: 'red'}).addTo(map);
L.polygon([[41.395648, 2.156006], [41.395546, 2.156052], [41.395492, 2.155844], [41.395697, 2.155752], [41.395750, 2.155960], [41.395648, 2.156006]], {color: 'red'}).addTo(map);
L.polygon([[41.395594, 2.155795], [41.395550, 2.155664], [41.395685, 2.155585], [41.395772, 2.155848], [41.395638, 2.155926], [41.395594, 2.155795]], {color: 'red'}).addTo(map);
L.polygon([[41.395640, 2.155768], [41.395600, 2.155635], [41.395758, 2.155550], [41.395839, 2.155816], [41.395680, 2.155901], [41.395640, 2.155768]], {color: 'red'}).addTo(map);
L.polygon([[41.395653, 2.155761], [41.395609, 2.155630], [41.395725, 2.155562], [41.395813, 2.155824], [41.395697, 2.155892], [41.395653, 2.155761]], {color: 'red'}).addTo(map);
L.polygon([[41.395709, 2.155728], [41.395616, 2.155802], [41.395550, 2.155655], [41.395735, 2.155507], [41.395802, 2.155654], [41.395709, 2.155728]], {color: 'red'}).addTo(map);
L.polygon([[41.395414, 2.155074], [41.395311, 2.155119], [41.395280, 2.154994], [41.395486, 2.154904], [41.395517, 2.155029], [41.395414, 2.155074]], {color: 'red'}).addTo(map);
L.polygon([[41.395383, 2.154948], [41.395281, 2.154995], [41.395222, 2.154768], [41.395427, 2.154675], [41.395485, 2.154901], [41.395383, 2.154948]], {color: 'red'}).addTo(map);
L.polygon([[41.395322, 2.154711], [41.395220, 2.154757], [41.395188, 2.154634], [41.395393, 2.154542], [41.395424, 2.154665], [41.395322, 2.154711]], {color: 'red'}).addTo(map);
L.polygon([[41.395293, 2.154597], [41.395190, 2.154642], [41.395161, 2.154524], [41.395367, 2.154435], [41.395396, 2.154552], [41.395293, 2.154597]], {color: 'red'}).addTo(map);
L.polygon([[41.395264, 2.154479], [41.395162, 2.154525], [41.395128, 2.154391], [41.395332, 2.154299], [41.395366, 2.154433], [41.395264, 2.154479]], {color: 'red'}).addTo(map);
L.polygon([[41.395230, 2.154345], [41.395128, 2.154391], [41.395096, 2.154264], [41.395300, 2.154173], [41.395332, 2.154299], [41.395230, 2.154345]], {color: 'red'}).addTo(map);
L.polygon([[41.395198, 2.154218], [41.395096, 2.154264], [41.395057, 2.154110], [41.395261, 2.154018], [41.395300, 2.154172], [41.395198, 2.154218]], {color: 'red'}).addTo(map);
L.polygon([[41.395159, 2.154064], [41.395057, 2.154110], [41.395027, 2.153994], [41.395231, 2.153901], [41.395261, 2.154018], [41.395159, 2.154064]], {color: 'red'}).addTo(map);
L.polygon([[41.395129, 2.153947], [41.395027, 2.153993], [41.394994, 2.153865], [41.395198, 2.153772], [41.395231, 2.153901], [41.395129, 2.153947]], {color: 'red'}).addTo(map);
L.polygon([[41.395096, 2.153818], [41.394994, 2.153864], [41.394954, 2.153709], [41.395158, 2.153616], [41.395198, 2.153772], [41.395096, 2.153818]], {color: 'red'}).addTo(map);
L.polygon([[41.395056, 2.153662], [41.394954, 2.153708], [41.394919, 2.153570], [41.395124, 2.153478], [41.395158, 2.153616], [41.395056, 2.153662]], {color: 'red'}).addTo(map);
L.polygon([[41.395021, 2.153523], [41.394919, 2.153569], [41.394890, 2.153455], [41.395094, 2.153363], [41.395123, 2.153477], [41.395021, 2.153523]], {color: 'red'}).addTo(map);
L.polygon([[41.394992, 2.153409], [41.394890, 2.153455], [41.394853, 2.153308], [41.395057, 2.153217], [41.395094, 2.153363], [41.394992, 2.153409]], {color: 'red'}).addTo(map);
L.polygon([[41.394955, 2.153262], [41.394853, 2.153309], [41.394817, 2.153170], [41.395021, 2.153077], [41.395057, 2.153215], [41.394955, 2.153262]], {color: 'red'}).addTo(map);
L.polygon([[41.394919, 2.153123], [41.394817, 2.153169], [41.394783, 2.153037], [41.394987, 2.152944], [41.395021, 2.153077], [41.394919, 2.153123]], {color: 'red'}).addTo(map);
L.polygon([[41.394885, 2.152990], [41.394782, 2.153035], [41.394749, 2.152904], [41.394954, 2.152813], [41.394988, 2.152945], [41.394885, 2.152990]], {color: 'red'}).addTo(map);
L.polygon([[41.394852, 2.152858], [41.394750, 2.152905], [41.394716, 2.152776], [41.394920, 2.152681], [41.394954, 2.152811], [41.394852, 2.152858]], {color: 'red'}).addTo(map);
L.polygon([[41.394818, 2.152728], [41.394716, 2.152774], [41.394652, 2.152524], [41.394857, 2.152432], [41.394920, 2.152682], [41.394818, 2.152728]], {color: 'red'}).addTo(map);
L.polygon([[41.394755, 2.152478], [41.394653, 2.152525], [41.394605, 2.152340], [41.394809, 2.152246], [41.394857, 2.152431], [41.394755, 2.152478]], {color: 'red'}).addTo(map);
L.polygon([[41.394707, 2.152292], [41.394605, 2.152339], [41.394559, 2.152159], [41.394763, 2.152066], [41.394809, 2.152245], [41.394707, 2.152292]], {color: 'red'}).addTo(map);
L.polygon([[41.394661, 2.152113], [41.394558, 2.152158], [41.394524, 2.152021], [41.394730, 2.151931], [41.394764, 2.152068], [41.394661, 2.152113]], {color: 'red'}).addTo(map);
L.polygon([[41.394627, 2.151976], [41.394525, 2.152023], [41.394495, 2.151907], [41.394699, 2.151814], [41.394729, 2.151929], [41.394627, 2.151976]], {color: 'red'}).addTo(map);
L.polygon([[41.394597, 2.151860], [41.394495, 2.151906], [41.394460, 2.151768], [41.394664, 2.151676], [41.394699, 2.151814], [41.394597, 2.151860]], {color: 'red'}).addTo(map);
L.polygon([[41.394562, 2.151722], [41.394460, 2.151768], [41.394429, 2.151648], [41.394633, 2.151556], [41.394664, 2.151676], [41.394562, 2.151722]], {color: 'red'}).addTo(map);
L.polygon([[41.394531, 2.151601], [41.394429, 2.151648], [41.394398, 2.151528], [41.394602, 2.151435], [41.394633, 2.151554], [41.394531, 2.151601]], {color: 'red'}).addTo(map);
L.polygon([[41.394500, 2.151481], [41.394398, 2.151528], [41.394357, 2.151370], [41.394561, 2.151276], [41.394602, 2.151434], [41.394500, 2.151481]], {color: 'red'}).addTo(map);
L.polygon([[41.394459, 2.151323], [41.394356, 2.151368], [41.394323, 2.151234], [41.394529, 2.151145], [41.394562, 2.151278], [41.394459, 2.151323]], {color: 'red'}).addTo(map);
L.polygon([[41.394426, 2.151189], [41.394323, 2.151234], [41.394291, 2.151103], [41.394496, 2.151012], [41.394529, 2.151144], [41.394426, 2.151189]], {color: 'red'}).addTo(map);
L.polygon([[41.394393, 2.151057], [41.394290, 2.151101], [41.394257, 2.150965], [41.394463, 2.150877], [41.394496, 2.151013], [41.394393, 2.151057]], {color: 'red'}).addTo(map);
L.polygon([[41.394360, 2.150921], [41.394258, 2.150967], [41.394224, 2.150836], [41.394429, 2.150744], [41.394462, 2.150875], [41.394360, 2.150921]], {color: 'red'}).addTo(map);
L.polygon([[41.394327, 2.150790], [41.394224, 2.150747], [41.394259, 2.150600], [41.394465, 2.150687], [41.394430, 2.150833], [41.394327, 2.150790]], {color: 'red'}).addTo(map);
L.polygon([[41.394389, 2.150530], [41.394355, 2.150545], [41.394323, 2.150418], [41.394391, 2.150387], [41.394423, 2.150515], [41.394389, 2.150530]], {color: 'red'}).addTo(map);
L.polygon([[41.394357, 2.150402], [41.394323, 2.150417], [41.394290, 2.150288], [41.394358, 2.150258], [41.394391, 2.150387], [41.394357, 2.150402]], {color: 'red'}).addTo(map);
L.polygon([[41.394324, 2.150273], [41.394290, 2.150288], [41.394259, 2.150165], [41.394327, 2.150135], [41.394358, 2.150258], [41.394324, 2.150273]], {color: 'red'}).addTo(map);
L.polygon([[41.394293, 2.150150], [41.394259, 2.150165], [41.394226, 2.150035], [41.394294, 2.150005], [41.394327, 2.150135], [41.394293, 2.150150]], {color: 'red'}).addTo(map);
L.polygon([[41.394260, 2.150020], [41.394226, 2.150035], [41.394194, 2.149908], [41.394262, 2.149877], [41.394294, 2.150005], [41.394260, 2.150020]], {color: 'red'}).addTo(map);
L.polygon([[41.394228, 2.149892], [41.394194, 2.149907], [41.394155, 2.149755], [41.394223, 2.149724], [41.394262, 2.149877], [41.394228, 2.149892]], {color: 'red'}).addTo(map);
L.polygon([[41.394189, 2.149739], [41.394155, 2.149754], [41.394121, 2.149618], [41.394189, 2.149588], [41.394223, 2.149724], [41.394189, 2.149739]], {color: 'red'}).addTo(map);
L.polygon([[41.394155, 2.149603], [41.394121, 2.149618], [41.394082, 2.149457], [41.394151, 2.149428], [41.394189, 2.149588], [41.394155, 2.149603]], {color: 'red'}).addTo(map);
L.polygon([[41.394117, 2.149445], [41.394082, 2.149458], [41.394054, 2.149322], [41.394124, 2.149297], [41.394152, 2.149432], [41.394117, 2.149445]], {color: 'red'}).addTo(map);
L.polygon([[41.394089, 2.149310], [41.394055, 2.149325], [41.394016, 2.149171], [41.394085, 2.149141], [41.394123, 2.149295], [41.394089, 2.149310]], {color: 'red'}).addTo(map);
L.polygon([[41.394050, 2.149153], [41.394016, 2.149168], [41.393979, 2.149024], [41.394047, 2.148993], [41.394084, 2.149138], [41.394050, 2.149153]], {color: 'red'}).addTo(map);
L.polygon([[41.394013, 2.149008], [41.393979, 2.149023], [41.393944, 2.148883], [41.394012, 2.148853], [41.394047, 2.148993], [41.394013, 2.149008]], {color: 'red'}).addTo(map);
L.polygon([[41.393978, 2.148868], [41.393944, 2.148883], [41.393909, 2.148744], [41.393977, 2.148714], [41.394012, 2.148853], [41.393978, 2.148868]], {color: 'red'}).addTo(map);
L.polygon([[41.393943, 2.148728], [41.393909, 2.148743], [41.393879, 2.148626], [41.393947, 2.148595], [41.393977, 2.148713], [41.393943, 2.148728]], {color: 'red'}).addTo(map);
L.polygon([[41.393913, 2.148610], [41.393879, 2.148626], [41.393837, 2.148469], [41.393905, 2.148437], [41.393947, 2.148594], [41.393913, 2.148610]], {color: 'red'}).addTo(map);
L.polygon([[41.393871, 2.148452], [41.393837, 2.148468], [41.393781, 2.148257], [41.393849, 2.148225], [41.393905, 2.148436], [41.393871, 2.148452]], {color: 'red'}).addTo(map);
L.polygon([[41.393815, 2.148241], [41.393781, 2.148257], [41.393742, 2.148110], [41.393810, 2.148078], [41.393849, 2.148225], [41.393815, 2.148241]], {color: 'red'}).addTo(map);
L.polygon([[41.393776, 2.148094], [41.393742, 2.148110], [41.393706, 2.147975], [41.393774, 2.147943], [41.393810, 2.148078], [41.393776, 2.148094]], {color: 'red'}).addTo(map);
L.polygon([[41.393740, 2.147958], [41.393706, 2.147974], [41.393674, 2.147853], [41.393742, 2.147821], [41.393774, 2.147942], [41.393740, 2.147958]], {color: 'red'}).addTo(map);
L.polygon([[41.393708, 2.147837], [41.393674, 2.147852], [41.393624, 2.147658], [41.393692, 2.147627], [41.393742, 2.147822], [41.393708, 2.147837]], {color: 'red'}).addTo(map);
L.polygon([[41.393658, 2.147642], [41.393624, 2.147657], [41.393586, 2.147506], [41.393654, 2.147476], [41.393692, 2.147627], [41.393658, 2.147642]], {color: 'red'}).addTo(map);
L.polygon([[41.393620, 2.147491], [41.393586, 2.147506], [41.393556, 2.147385], [41.393625, 2.147356], [41.393654, 2.147476], [41.393620, 2.147491]], {color: 'red'}).addTo(map);
L.polygon([[41.393591, 2.147371], [41.393557, 2.147386], [41.393526, 2.147265], [41.393594, 2.147235], [41.393625, 2.147356], [41.393591, 2.147371]], {color: 'red'}).addTo(map);
L.polygon([[41.393560, 2.147249], [41.393526, 2.147264], [41.393487, 2.147108], [41.393555, 2.147078], [41.393594, 2.147234], [41.393560, 2.147249]], {color: 'red'}).addTo(map);
L.polygon([[41.393521, 2.147093], [41.393487, 2.147108], [41.393451, 2.146962], [41.393519, 2.146932], [41.393555, 2.147078], [41.393521, 2.147093]], {color: 'red'}).addTo(map);
L.polygon([[41.393485, 2.146947], [41.393467, 2.146989], [41.393372, 2.146917], [41.393408, 2.146834], [41.393503, 2.146905], [41.393485, 2.146947]], {color: 'red'}).addTo(map);
L.polygon([[41.393326, 2.146827], [41.393223, 2.146872], [41.393194, 2.146756], [41.393399, 2.146666], [41.393429, 2.146782], [41.393326, 2.146827]], {color: 'red'}).addTo(map);
L.polygon([[41.393297, 2.146711], [41.393195, 2.146757], [41.393164, 2.146636], [41.393368, 2.146544], [41.393399, 2.146665], [41.393297, 2.146711]], {color: 'red'}).addTo(map);
L.polygon([[41.393266, 2.146589], [41.393163, 2.146634], [41.393131, 2.146507], [41.393336, 2.146416], [41.393369, 2.146544], [41.393266, 2.146589]], {color: 'red'}).addTo(map);
L.polygon([[41.393234, 2.146461], [41.393132, 2.146507], [41.393093, 2.146354], [41.393297, 2.146262], [41.393336, 2.146415], [41.393234, 2.146461]], {color: 'red'}).addTo(map);
L.polygon([[41.393195, 2.146308], [41.393092, 2.146353], [41.393061, 2.146228], [41.393266, 2.146138], [41.393298, 2.146263], [41.393195, 2.146308]], {color: 'red'}).addTo(map);
L.polygon([[41.393164, 2.146183], [41.393062, 2.146229], [41.393032, 2.146111], [41.393236, 2.146019], [41.393266, 2.146137], [41.393164, 2.146183]], {color: 'red'}).addTo(map);
L.polygon([[41.393134, 2.146065], [41.393032, 2.146112], [41.392999, 2.145983], [41.393203, 2.145890], [41.393236, 2.146018], [41.393134, 2.146065]], {color: 'red'}).addTo(map);
L.polygon([[41.393099, 2.145929], [41.392997, 2.145975], [41.392961, 2.145833], [41.393166, 2.145742], [41.393201, 2.145883], [41.393099, 2.145929]], {color: 'red'}).addTo(map);
L.polygon([[41.393065, 2.145794], [41.392962, 2.145839], [41.392925, 2.145687], [41.393130, 2.145598], [41.393168, 2.145749], [41.393065, 2.145794]], {color: 'red'}).addTo(map);
L.polygon([[41.393027, 2.145639], [41.392924, 2.145684], [41.392885, 2.145525], [41.393091, 2.145436], [41.393130, 2.145594], [41.393027, 2.145639]], {color: 'red'}).addTo(map);
L.polygon([[41.392989, 2.145484], [41.392886, 2.145529], [41.392848, 2.145375], [41.393054, 2.145285], [41.393092, 2.145439], [41.392989, 2.145484]], {color: 'red'}).addTo(map);
L.polygon([[41.392951, 2.145330], [41.392848, 2.145375], [41.392819, 2.145258], [41.393025, 2.145168], [41.393054, 2.145285], [41.392951, 2.145330]], {color: 'red'}).addTo(map);
L.polygon([[41.392922, 2.145213], [41.392820, 2.145262], [41.392778, 2.145108], [41.392982, 2.145010], [41.393024, 2.145164], [41.392922, 2.145213]], {color: 'red'}).addTo(map);
L.polygon([[41.392881, 2.145062], [41.392785, 2.144996], [41.392829, 2.144883], [41.393021, 2.145014], [41.392977, 2.145128], [41.392881, 2.145062]], {color: 'red'}).addTo(map);
L.polygon([[41.392943, 2.144901], [41.392820, 2.144726], [41.392891, 2.144639], [41.393136, 2.144990], [41.393066, 2.145076], [41.392943, 2.144901]], {color: 'red'}).addTo(map);
L.polygon([[41.393010, 2.144818], [41.392856, 2.144693], [41.392921, 2.144553], [41.393228, 2.144802], [41.393164, 2.144943], [41.393010, 2.144818]], {color: 'red'}).addTo(map);
L.polygon([[41.393075, 2.144676], [41.392900, 2.144618], [41.392922, 2.144501], [41.393272, 2.144618], [41.393250, 2.144734], [41.393075, 2.144676]], {color: 'red'}).addTo(map);
L.polygon([[41.393098, 2.144554], [41.392918, 2.144565], [41.392914, 2.144432], [41.393273, 2.144410], [41.393278, 2.144543], [41.393098, 2.144554]], {color: 'red'}).addTo(map);
L.polygon([[41.393093, 2.144404], [41.392927, 2.144497], [41.392875, 2.144333], [41.393206, 2.144146], [41.393259, 2.144311], [41.393093, 2.144404]], {color: 'red'}).addTo(map);
L.polygon([[41.393035, 2.144222], [41.392914, 2.144399], [41.392806, 2.144269], [41.393049, 2.143916], [41.393156, 2.144045], [41.393035, 2.144222]], {color: 'red'}).addTo(map);
L.polygon([[41.392915, 2.144076], [41.392851, 2.144299], [41.392752, 2.144249], [41.392880, 2.143802], [41.392979, 2.143853], [41.392915, 2.144076]], {color: 'red'}).addTo(map);
L.polygon([[41.392799, 2.144017], [41.392785, 2.144255], [41.392676, 2.144244], [41.392704, 2.143767], [41.392813, 2.143779], [41.392799, 2.144017]], {color: 'red'}).addTo(map);
L.polygon([[41.392695, 2.144006], [41.392578, 2.144188], [41.392496, 2.144095], [41.392729, 2.143731], [41.392812, 2.143824], [41.392695, 2.144006]], {color: 'red'}).addTo(map);
L.polygon([[41.392619, 2.143920], [41.392515, 2.144052], [41.392447, 2.143957], [41.392655, 2.143693], [41.392723, 2.143788], [41.392619, 2.143920]], {color: 'red'}).addTo(map);
L.polygon([[41.392553, 2.143828], [41.392433, 2.143935], [41.392371, 2.143812], [41.392610, 2.143598], [41.392673, 2.143721], [41.392553, 2.143828]], {color: 'red'}).addTo(map);
L.polygon([[41.392498, 2.143719], [41.392396, 2.143765], [41.392350, 2.143588], [41.392555, 2.143495], [41.392600, 2.143673], [41.392498, 2.143719]], {color: 'red'}).addTo(map);
L.polygon([[41.392453, 2.143543], [41.392351, 2.143589], [41.392296, 2.143375], [41.392500, 2.143282], [41.392555, 2.143497], [41.392453, 2.143543]], {color: 'red'}).addTo(map);
L.polygon([[41.392398, 2.143328], [41.392296, 2.143375], [41.392240, 2.143160], [41.392444, 2.143066], [41.392500, 2.143281], [41.392398, 2.143328]], {color: 'red'}).addTo(map);
L.polygon([[41.392342, 2.143112], [41.392240, 2.143159], [41.392180, 2.142926], [41.392384, 2.142833], [41.392444, 2.143065], [41.392342, 2.143112]], {color: 'red'}).addTo(map);
L.polygon([[41.392282, 2.142879], [41.392180, 2.142925], [41.392124, 2.142708], [41.392328, 2.142615], [41.392384, 2.142833], [41.392282, 2.142879]], {color: 'red'}).addTo(map);
L.polygon([[41.392226, 2.142661], [41.392124, 2.142708], [41.392086, 2.142561], [41.392290, 2.142468], [41.392328, 2.142614], [41.392226, 2.142661]], {color: 'red'}).addTo(map);
L.polygon([[41.392188, 2.142514], [41.392086, 2.142560], [41.392027, 2.142330], [41.392231, 2.142238], [41.392290, 2.142468], [41.392188, 2.142514]], {color: 'red'}).addTo(map);
L.polygon([[41.392129, 2.142283], [41.392027, 2.142329], [41.391997, 2.142212], [41.392201, 2.142119], [41.392231, 2.142237], [41.392129, 2.142283]], {color: 'red'}).addTo(map);
L.polygon([[41.392099, 2.142166], [41.391997, 2.142213], [41.391965, 2.142090], [41.392169, 2.141996], [41.392201, 2.142119], [41.392099, 2.142166]], {color: 'red'}).addTo(map);
L.polygon([[41.392067, 2.142042], [41.391965, 2.142088], [41.391932, 2.141958], [41.392136, 2.141866], [41.392169, 2.141996], [41.392067, 2.142042]], {color: 'red'}).addTo(map);
L.polygon([[41.392034, 2.141912], [41.391932, 2.141959], [41.391870, 2.141719], [41.392074, 2.141626], [41.392136, 2.141865], [41.392034, 2.141912]], {color: 'red'}).addTo(map);
L.polygon([[41.391972, 2.141672], [41.391870, 2.141718], [41.391841, 2.141603], [41.392045, 2.141512], [41.392074, 2.141626], [41.391972, 2.141672]], {color: 'red'}).addTo(map);
L.polygon([[41.391943, 2.141557], [41.391841, 2.141603], [41.391804, 2.141460], [41.392008, 2.141367], [41.392045, 2.141511], [41.391943, 2.141557]], {color: 'red'}).addTo(map);
L.polygon([[41.391906, 2.141413], [41.391770, 2.141475], [41.391740, 2.141360], [41.392012, 2.141235], [41.392042, 2.141351], [41.391906, 2.141413]], {color: 'red'}).addTo(map);
L.polygon([[41.391876, 2.141297], [41.391739, 2.141358], [41.391707, 2.141231], [41.391980, 2.141109], [41.392013, 2.141236], [41.391876, 2.141297]], {color: 'red'}).addTo(map);
L.polygon([[41.391844, 2.141170], [41.391707, 2.141231], [41.391673, 2.141097], [41.391947, 2.140975], [41.391981, 2.141109], [41.391844, 2.141170]], {color: 'red'}).addTo(map);
L.polygon([[41.391810, 2.141035], [41.391674, 2.141097], [41.391642, 2.140972], [41.391914, 2.140848], [41.391946, 2.140973], [41.391810, 2.141035]], {color: 'red'}).addTo(map);
L.polygon([[41.391778, 2.140910], [41.391642, 2.140972], [41.391612, 2.140857], [41.391884, 2.140732], [41.391914, 2.140848], [41.391778, 2.140910]], {color: 'red'}).addTo(map);
L.polygon([[41.391748, 2.140794], [41.391612, 2.140855], [41.391558, 2.140646], [41.391831, 2.140524], [41.391884, 2.140733], [41.391748, 2.140794]], {color: 'red'}).addTo(map);
L.polygon([[41.391695, 2.140585], [41.391558, 2.140646], [41.391502, 2.140425], [41.391776, 2.140303], [41.391832, 2.140524], [41.391695, 2.140585]], {color: 'red'}).addTo(map);
L.polygon([[41.391639, 2.140363], [41.391503, 2.140425], [41.391445, 2.140198], [41.391717, 2.140075], [41.391775, 2.140301], [41.391639, 2.140363]], {color: 'red'}).addTo(map);
L.polygon([[41.391581, 2.140136], [41.391444, 2.140197], [41.391391, 2.139987], [41.391664, 2.139865], [41.391718, 2.140075], [41.391581, 2.140136]], {color: 'red'}).addTo(map);
L.polygon([[41.391528, 2.139926], [41.391391, 2.139987], [41.391341, 2.139789], [41.391615, 2.139667], [41.391665, 2.139865], [41.391528, 2.139926]], {color: 'red'}).addTo(map);
L.polygon([[41.391477, 2.139723], [41.391341, 2.139784], [41.391284, 2.139564], [41.391557, 2.139441], [41.391613, 2.139662], [41.391477, 2.139723]], {color: 'red'}).addTo(map);
L.polygon([[41.391422, 2.139506], [41.391286, 2.139568], [41.391231, 2.139354], [41.391503, 2.139230], [41.391558, 2.139444], [41.391422, 2.139506]], {color: 'red'}).addTo(map);
L.polygon([[41.391367, 2.139292], [41.391231, 2.139356], [41.391200, 2.139240], [41.391472, 2.139112], [41.391503, 2.139228], [41.391367, 2.139292]], {color: 'red'}).addTo(map);
L.polygon([[41.391336, 2.139176], [41.391200, 2.139239], [41.391166, 2.139110], [41.391438, 2.138984], [41.391472, 2.139113], [41.391336, 2.139176]], {color: 'red'}).addTo(map);
L.polygon([[41.391272, 2.138932], [41.391136, 2.138995], [41.391079, 2.138777], [41.391351, 2.138652], [41.391408, 2.138869], [41.391272, 2.138932]], {color: 'red'}).addTo(map);
L.polygon([[41.391215, 2.138713], [41.391079, 2.138774], [41.391027, 2.138573], [41.391300, 2.138450], [41.391351, 2.138652], [41.391215, 2.138713]], {color: 'red'}).addTo(map);
L.polygon([[41.391164, 2.138512], [41.391027, 2.138572], [41.390997, 2.138453], [41.391271, 2.138332], [41.391301, 2.138452], [41.391164, 2.138512]], {color: 'red'}).addTo(map);
L.polygon([[41.391134, 2.138392], [41.390998, 2.138453], [41.390950, 2.138265], [41.391222, 2.138143], [41.391270, 2.138331], [41.391134, 2.138392]], {color: 'red'}).addTo(map);
L.polygon([[41.391086, 2.138203], [41.390950, 2.138264], [41.390891, 2.138035], [41.391164, 2.137913], [41.391222, 2.138142], [41.391086, 2.138203]], {color: 'red'}).addTo(map);
L.polygon([[41.391028, 2.137974], [41.390892, 2.138036], [41.390861, 2.137917], [41.391133, 2.137792], [41.391164, 2.137912], [41.391028, 2.137974]], {color: 'red'}).addTo(map);
L.polygon([[41.390997, 2.137854], [41.390860, 2.137915], [41.390807, 2.137704], [41.391081, 2.137583], [41.391134, 2.137793], [41.390997, 2.137854]], {color: 'red'}).addTo(map);
L.polygon([[41.390944, 2.137643], [41.390807, 2.137704], [41.390777, 2.137585], [41.391051, 2.137463], [41.391081, 2.137582], [41.390944, 2.137643]], {color: 'red'}).addTo(map);
L.polygon([[41.390914, 2.137524], [41.390777, 2.137585], [41.390741, 2.137442], [41.391015, 2.137321], [41.391051, 2.137463], [41.390914, 2.137524]], {color: 'red'}).addTo(map);
L.polygon([[41.390878, 2.137381], [41.390741, 2.137441], [41.390708, 2.137308], [41.390982, 2.137188], [41.391015, 2.137321], [41.390878, 2.137381]], {color: 'red'}).addTo(map);
L.polygon([[41.390845, 2.137248], [41.390709, 2.137310], [41.390677, 2.137186], [41.390949, 2.137062], [41.390981, 2.137186], [41.390845, 2.137248]], {color: 'red'}).addTo(map);
L.polygon([[41.390813, 2.137124], [41.390677, 2.137185], [41.390641, 2.137048], [41.390914, 2.136925], [41.390949, 2.137063], [41.390813, 2.137124]], {color: 'red'}).addTo(map);
L.polygon([[41.390778, 2.136986], [41.390642, 2.137048], [41.390605, 2.136904], [41.390878, 2.136780], [41.390914, 2.136924], [41.390778, 2.136986]], {color: 'red'}).addTo(map);
L.polygon([[41.390739, 2.136833], [41.390602, 2.136894], [41.390571, 2.136770], [41.390844, 2.136648], [41.390876, 2.136772], [41.390739, 2.136833]], {color: 'red'}).addTo(map);
L.polygon([[41.390710, 2.136718], [41.390574, 2.136780], [41.390521, 2.136573], [41.390793, 2.136450], [41.390846, 2.136656], [41.390710, 2.136718]], {color: 'red'}).addTo(map);
L.polygon([[41.390657, 2.136511], [41.390520, 2.136572], [41.390464, 2.136349], [41.390738, 2.136228], [41.390794, 2.136450], [41.390657, 2.136511]], {color: 'red'}).addTo(map);
L.polygon([[41.390601, 2.136288], [41.390465, 2.136350], [41.390404, 2.136115], [41.390677, 2.135992], [41.390737, 2.136226], [41.390601, 2.136288]], {color: 'red'}).addTo(map);
L.polygon([[41.390541, 2.136053], [41.390405, 2.136115], [41.390369, 2.135977], [41.390641, 2.135852], [41.390677, 2.135991], [41.390541, 2.136053]], {color: 'red'}).addTo(map);
L.polygon([[41.390505, 2.135914], [41.390369, 2.135975], [41.390335, 2.135842], [41.390608, 2.135720], [41.390641, 2.135853], [41.390505, 2.135914]], {color: 'red'}).addTo(map);
L.polygon([[41.390471, 2.135780], [41.390334, 2.135840], [41.390300, 2.135703], [41.390574, 2.135583], [41.390608, 2.135720], [41.390471, 2.135780]], {color: 'red'}).addTo(map);
L.polygon([[41.390437, 2.135643], [41.390301, 2.135705], [41.390261, 2.135551], [41.390533, 2.135426], [41.390573, 2.135581], [41.390437, 2.135643]], {color: 'red'}).addTo(map);
L.polygon([[41.390397, 2.135488], [41.390261, 2.135549], [41.390223, 2.135403], [41.390496, 2.135281], [41.390533, 2.135427], [41.390397, 2.135488]], {color: 'red'}).addTo(map);
L.polygon([[41.390360, 2.135342], [41.390224, 2.135404], [41.390185, 2.135251], [41.390457, 2.135128], [41.390496, 2.135280], [41.390360, 2.135342]], {color: 'red'}).addTo(map);
L.polygon([[41.390321, 2.135189], [41.390185, 2.135250], [41.390154, 2.135133], [41.390427, 2.135010], [41.390457, 2.135128], [41.390321, 2.135189]], {color: 'red'}).addTo(map);
L.polygon([[41.390291, 2.135071], [41.390154, 2.135132], [41.390111, 2.134961], [41.390384, 2.134839], [41.390428, 2.135010], [41.390291, 2.135071]], {color: 'red'}).addTo(map);
L.polygon([[41.390248, 2.134900], [41.390112, 2.134961], [41.390074, 2.134815], [41.390347, 2.134693], [41.390384, 2.134839], [41.390248, 2.134900]], {color: 'red'}).addTo(map);
L.polygon([[41.390211, 2.134754], [41.390075, 2.134816], [41.390038, 2.134673], [41.390310, 2.134549], [41.390347, 2.134692], [41.390211, 2.134754]], {color: 'red'}).addTo(map);
L.polygon([[41.390174, 2.134610], [41.390038, 2.134671], [41.390001, 2.134526], [41.390274, 2.134404], [41.390310, 2.134549], [41.390174, 2.134610]], {color: 'red'}).addTo(map);
L.polygon([[41.390137, 2.134464], [41.390001, 2.134525], [41.389966, 2.134391], [41.390239, 2.134268], [41.390273, 2.134403], [41.390137, 2.134464]], {color: 'red'}).addTo(map);
L.polygon([[41.390103, 2.134330], [41.389967, 2.134391], [41.389928, 2.134238], [41.390201, 2.134116], [41.390239, 2.134269], [41.390103, 2.134330]], {color: 'red'}).addTo(map);
L.polygon([[41.390064, 2.134176], [41.389928, 2.134238], [41.389897, 2.134119], [41.390169, 2.133995], [41.390200, 2.134114], [41.390064, 2.134176]], {color: 'red'}).addTo(map);
L.polygon([[41.390033, 2.134056], [41.389897, 2.134118], [41.389858, 2.133968], [41.390131, 2.133845], [41.390169, 2.133994], [41.390033, 2.134056]], {color: 'red'}).addTo(map);
L.polygon([[41.389995, 2.133907], [41.389858, 2.133967], [41.389829, 2.133852], [41.390103, 2.133731], [41.390132, 2.133847], [41.389995, 2.133907]], {color: 'red'}).addTo(map);
L.polygon([[41.389966, 2.133791], [41.389830, 2.133853], [41.389792, 2.133706], [41.390064, 2.133582], [41.390102, 2.133729], [41.389966, 2.133791]], {color: 'red'}).addTo(map);
L.polygon([[41.389928, 2.133643], [41.389792, 2.133705], [41.389761, 2.133584], [41.390033, 2.133460], [41.390064, 2.133581], [41.389928, 2.133643]], {color: 'red'}).addTo(map);
L.polygon([[41.389897, 2.133522], [41.389760, 2.133583], [41.389706, 2.133369], [41.389979, 2.133248], [41.390034, 2.133461], [41.389897, 2.133522]], {color: 'red'}).addTo(map);
L.polygon([[41.389843, 2.133308], [41.389707, 2.133370], [41.389673, 2.133241], [41.389946, 2.133117], [41.389979, 2.133246], [41.389843, 2.133308]], {color: 'red'}).addTo(map);
L.polygon([[41.389810, 2.133179], [41.389674, 2.133240], [41.389642, 2.133119], [41.389915, 2.132996], [41.389946, 2.133118], [41.389810, 2.133179]], {color: 'red'}).addTo(map);
L.polygon([[41.389779, 2.133057], [41.389642, 2.133118], [41.389612, 2.133000], [41.389886, 2.132878], [41.389916, 2.132996], [41.389779, 2.133057]], {color: 'red'}).addTo(map);
L.polygon([[41.389749, 2.132938], [41.389613, 2.133000], [41.389579, 2.132867], [41.389851, 2.132743], [41.389885, 2.132876], [41.389749, 2.132938]], {color: 'red'}).addTo(map);
L.polygon([[41.389715, 2.132805], [41.389578, 2.132866], [41.389547, 2.132743], [41.389821, 2.132622], [41.389852, 2.132744], [41.389715, 2.132805]], {color: 'red'}).addTo(map);
L.polygon([[41.389684, 2.132682], [41.389548, 2.132744], [41.389516, 2.132619], [41.389788, 2.132495], [41.389820, 2.132620], [41.389684, 2.132682]], {color: 'red'}).addTo(map);
L.polygon([[41.389652, 2.132557], [41.389516, 2.132619], [41.389480, 2.132480], [41.389752, 2.132356], [41.389788, 2.132495], [41.389652, 2.132557]], {color: 'red'}).addTo(map);
L.polygon([[41.389616, 2.132418], [41.389480, 2.132482], [41.389423, 2.132270], [41.389695, 2.132142], [41.389752, 2.132354], [41.389616, 2.132418]], {color: 'red'}).addTo(map);
L.polygon([[41.389559, 2.132205], [41.389423, 2.132266], [41.389369, 2.132058], [41.389642, 2.131935], [41.389695, 2.132144], [41.389559, 2.132205]], {color: 'red'}).addTo(map);
L.polygon([[41.389506, 2.131996], [41.389369, 2.132057], [41.389319, 2.131859], [41.389593, 2.131737], [41.389643, 2.131935], [41.389506, 2.131996]], {color: 'red'}).addTo(map);
L.polygon([[41.389456, 2.131797], [41.389320, 2.131858], [41.389282, 2.131712], [41.389555, 2.131589], [41.389592, 2.131736], [41.389456, 2.131797]], {color: 'red'}).addTo(map);
L.polygon([[41.389419, 2.131651], [41.389282, 2.131711], [41.389246, 2.131568], [41.389520, 2.131447], [41.389556, 2.131591], [41.389419, 2.131651]], {color: 'red'}).addTo(map);
L.polygon([[41.389383, 2.131507], [41.389246, 2.131567], [41.389217, 2.131452], [41.389491, 2.131331], [41.389520, 2.131447], [41.389383, 2.131507]], {color: 'red'}).addTo(map);
L.polygon([[41.389354, 2.131391], [41.389218, 2.131454], [41.389185, 2.131327], [41.389457, 2.131201], [41.389490, 2.131328], [41.389354, 2.131391]], {color: 'red'}).addTo(map);
L.polygon([[41.389321, 2.131264], [41.389184, 2.131324], [41.389142, 2.131157], [41.389416, 2.131036], [41.389458, 2.131204], [41.389321, 2.131264]], {color: 'red'}).addTo(map);
L.polygon([[41.389279, 2.131096], [41.389142, 2.131156], [41.389103, 2.130998], [41.389377, 2.130878], [41.389416, 2.131036], [41.389279, 2.131096]], {color: 'red'}).addTo(map);
L.polygon([[41.389240, 2.130938], [41.389104, 2.131000], [41.389066, 2.130854], [41.389338, 2.130729], [41.389376, 2.130876], [41.389240, 2.130938]], {color: 'red'}).addTo(map);
L.polygon([[41.389202, 2.130791], [41.389065, 2.130850], [41.389036, 2.130732], [41.389310, 2.130614], [41.389339, 2.130732], [41.389202, 2.130791]], {color: 'red'}).addTo(map);
L.polygon([[41.389173, 2.130672], [41.389037, 2.130734], [41.388996, 2.130574], [41.389268, 2.130450], [41.389309, 2.130610], [41.389173, 2.130672]], {color: 'red'}).addTo(map);
L.polygon([[41.389132, 2.130512], [41.388995, 2.130572], [41.388960, 2.130432], [41.389234, 2.130312], [41.389269, 2.130452], [41.389132, 2.130512]], {color: 'red'}).addTo(map);
L.polygon([[41.389097, 2.130371], [41.388961, 2.130432], [41.388931, 2.130315], [41.389203, 2.130192], [41.389233, 2.130310], [41.389097, 2.130371]], {color: 'red'}).addTo(map);
L.polygon([[41.389067, 2.130253], [41.388930, 2.130313], [41.388897, 2.130182], [41.389171, 2.130061], [41.389204, 2.130193], [41.389067, 2.130253]], {color: 'red'}).addTo(map);
L.polygon([[41.389034, 2.130121], [41.388897, 2.130182], [41.388864, 2.130050], [41.389137, 2.129929], [41.389171, 2.130060], [41.389034, 2.130121]], {color: 'red'}).addTo(map);
L.polygon([[41.389001, 2.129990], [41.388864, 2.130051], [41.388833, 2.129925], [41.389106, 2.129803], [41.389138, 2.129929], [41.389001, 2.129990]], {color: 'red'}).addTo(map);
L.polygon([[41.388969, 2.129863], [41.388833, 2.129924], [41.388788, 2.129751], [41.389061, 2.129628], [41.389105, 2.129802], [41.388969, 2.129863]], {color: 'red'}).addTo(map);
L.polygon([[41.388925, 2.129690], [41.388789, 2.129752], [41.388759, 2.129636], [41.389031, 2.129512], [41.389061, 2.129628], [41.388925, 2.129690]], {color: 'red'}).addTo(map);
L.polygon([[41.388895, 2.129573], [41.388758, 2.129633], [41.388724, 2.129498], [41.388998, 2.129377], [41.389032, 2.129513], [41.388895, 2.129573]], {color: 'red'}).addTo(map);
L.polygon([[41.388861, 2.129437], [41.388725, 2.129498], [41.388687, 2.129352], [41.388960, 2.129230], [41.388997, 2.129376], [41.388861, 2.129437]], {color: 'red'}).addTo(map);
L.polygon([[41.388824, 2.129291], [41.388687, 2.129352], [41.388651, 2.129209], [41.388925, 2.129088], [41.388961, 2.129230], [41.388824, 2.129291]], {color: 'red'}).addTo(map);
L.polygon([[41.388788, 2.129148], [41.388651, 2.129209], [41.388622, 2.129094], [41.388896, 2.128972], [41.388925, 2.129087], [41.388788, 2.129148]], {color: 'red'}).addTo(map);
L.polygon([[41.388759, 2.129033], [41.388622, 2.129093], [41.388590, 2.128966], [41.388864, 2.128845], [41.388896, 2.128973], [41.388759, 2.129033]], {color: 'red'}).addTo(map);
L.polygon([[41.388727, 2.128905], [41.388591, 2.128967], [41.388559, 2.128844], [41.388831, 2.128719], [41.388863, 2.128843], [41.388727, 2.128905]], {color: 'red'}).addTo(map);
L.polygon([[41.388695, 2.128781], [41.388558, 2.128841], [41.388527, 2.128714], [41.388801, 2.128595], [41.388832, 2.128721], [41.388695, 2.128781]], {color: 'red'}).addTo(map);
L.polygon([[41.388664, 2.128655], [41.388528, 2.128717], [41.388487, 2.128559], [41.388759, 2.128434], [41.388800, 2.128593], [41.388664, 2.128655]], {color: 'red'}).addTo(map);
L.polygon([[41.388623, 2.128496], [41.388486, 2.128556], [41.388443, 2.128383], [41.388717, 2.128263], [41.388760, 2.128436], [41.388623, 2.128496]], {color: 'red'}).addTo(map);
L.polygon([[41.388580, 2.128323], [41.388444, 2.128385], [41.388398, 2.128208], [41.388671, 2.128085], [41.388716, 2.128261], [41.388580, 2.128323]], {color: 'red'}).addTo(map);
L.polygon([[41.388533, 2.128139], [41.388396, 2.128200], [41.388351, 2.128018], [41.388624, 2.127897], [41.388670, 2.128078], [41.388533, 2.128139]], {color: 'red'}).addTo(map);
L.polygon([[41.388489, 2.127964], [41.388352, 2.128025], [41.388308, 2.127850], [41.388581, 2.127728], [41.388626, 2.127903], [41.388489, 2.127964]], {color: 'red'}).addTo(map);
L.polygon([[41.388445, 2.127789], [41.388308, 2.127850], [41.388267, 2.127687], [41.388540, 2.127565], [41.388582, 2.127728], [41.388445, 2.127789]], {color: 'red'}).addTo(map);
L.polygon([[41.388404, 2.127626], [41.388268, 2.127687], [41.388221, 2.127507], [41.388494, 2.127384], [41.388540, 2.127565], [41.388404, 2.127626]], {color: 'red'}).addTo(map);
L.polygon([[41.388358, 2.127445], [41.388221, 2.127505], [41.388180, 2.127340], [41.388454, 2.127220], [41.388495, 2.127385], [41.388358, 2.127445]], {color: 'red'}).addTo(map);
L.polygon([[41.388317, 2.127280], [41.388181, 2.127341], [41.388149, 2.127219], [41.388422, 2.127097], [41.388453, 2.127219], [41.388317, 2.127280]], {color: 'red'}).addTo(map);
L.polygon([[41.388286, 2.127158], [41.388149, 2.127219], [41.388112, 2.127069], [41.388385, 2.126947], [41.388423, 2.127097], [41.388286, 2.127158]], {color: 'red'}).addTo(map);
L.polygon([[41.388248, 2.127007], [41.388112, 2.127068], [41.388073, 2.126918], [41.388346, 2.126796], [41.388384, 2.126946], [41.388248, 2.127007]], {color: 'red'}).addTo(map);
L.polygon([[41.388210, 2.126857], [41.388073, 2.126918], [41.388033, 2.126759], [41.388306, 2.126637], [41.388347, 2.126796], [41.388210, 2.126857]], {color: 'red'}).addTo(map);
L.polygon([[41.388170, 2.126698], [41.388033, 2.126759], [41.387988, 2.126580], [41.388261, 2.126458], [41.388307, 2.126637], [41.388170, 2.126698]], {color: 'red'}).addTo(map);
L.polygon([[41.388125, 2.126519], [41.387988, 2.126579], [41.387949, 2.126421], [41.388223, 2.126302], [41.388262, 2.126459], [41.388125, 2.126519]], {color: 'red'}).addTo(map);
L.polygon([[41.388086, 2.126361], [41.387950, 2.126423], [41.387903, 2.126241], [41.388175, 2.126117], [41.388222, 2.126299], [41.388086, 2.126361]], {color: 'red'}).addTo(map);
L.polygon([[41.388039, 2.126178], [41.387902, 2.126239], [41.387871, 2.126116], [41.388144, 2.125994], [41.388176, 2.126117], [41.388039, 2.126178]], {color: 'red'}).addTo(map);
L.polygon([[41.388008, 2.126055], [41.387871, 2.126115], [41.387843, 2.126001], [41.388117, 2.125882], [41.388145, 2.125995], [41.388008, 2.126055]], {color: 'red'}).addTo(map);
L.polygon([[41.387980, 2.125941], [41.387843, 2.126001], [41.387814, 2.125885], [41.388088, 2.125765], [41.388117, 2.125881], [41.387980, 2.125941]], {color: 'red'}).addTo(map);
L.polygon([[41.387951, 2.125824], [41.387814, 2.125885], [41.387772, 2.125717], [41.388045, 2.125596], [41.388088, 2.125763], [41.387951, 2.125824]], {color: 'red'}).addTo(map);
L.polygon([[41.387909, 2.125657], [41.387773, 2.125718], [41.387740, 2.125593], [41.388013, 2.125470], [41.388045, 2.125596], [41.387909, 2.125657]], {color: 'red'}).addTo(map);
L.polygon([[41.387877, 2.125531], [41.387740, 2.125592], [41.387694, 2.125410], [41.387967, 2.125288], [41.388014, 2.125470], [41.387877, 2.125531]], {color: 'red'}).addTo(map);
L.polygon([[41.387831, 2.125349], [41.387694, 2.125409], [41.387666, 2.125295], [41.387939, 2.125174], [41.387968, 2.125289], [41.387831, 2.125349]], {color: 'red'}).addTo(map);
L.polygon([[41.387802, 2.125233], [41.387665, 2.125291], [41.387636, 2.125172], [41.387911, 2.125056], [41.387939, 2.125175], [41.387802, 2.125233]], {color: 'red'}).addTo(map);
L.polygon([[41.387774, 2.125115], [41.387637, 2.125176], [41.387596, 2.125013], [41.387870, 2.124892], [41.387911, 2.125054], [41.387774, 2.125115]], {color: 'red'}).addTo(map);
L.polygon([[41.387733, 2.124952], [41.387596, 2.125013], [41.387564, 2.124886], [41.387837, 2.124764], [41.387870, 2.124891], [41.387733, 2.124952]], {color: 'red'}).addTo(map);
L.polygon([[41.387701, 2.124825], [41.387564, 2.124884], [41.387534, 2.124760], [41.387808, 2.124642], [41.387838, 2.124766], [41.387701, 2.124825]], {color: 'red'}).addTo(map);
L.polygon([[41.387671, 2.124701], [41.387534, 2.124762], [41.387500, 2.124624], [41.387773, 2.124502], [41.387808, 2.124640], [41.387671, 2.124701]], {color: 'red'}).addTo(map);
L.polygon([[41.387636, 2.124562], [41.387499, 2.124622], [41.387457, 2.124452], [41.387731, 2.124332], [41.387773, 2.124502], [41.387636, 2.124562]], {color: 'red'}).addTo(map);
L.polygon([[41.387594, 2.124392], [41.387457, 2.124452], [41.387421, 2.124308], [41.387695, 2.124188], [41.387731, 2.124332], [41.387594, 2.124392]], {color: 'red'}).addTo(map);
L.polygon([[41.387558, 2.124247], [41.387421, 2.124307], [41.387384, 2.124156], [41.387658, 2.124037], [41.387695, 2.124187], [41.387558, 2.124247]], {color: 'red'}).addTo(map);
L.polygon([[41.387521, 2.124097], [41.387384, 2.124158], [41.387341, 2.123984], [41.387614, 2.123862], [41.387658, 2.124036], [41.387521, 2.124097]], {color: 'red'}).addTo(map);
L.polygon([[41.387477, 2.123922], [41.387340, 2.123982], [41.387312, 2.123868], [41.387585, 2.123748], [41.387614, 2.123862], [41.387477, 2.123922]], {color: 'red'}).addTo(map);
L.polygon([[41.387449, 2.123809], [41.387312, 2.123869], [41.387284, 2.123755], [41.387558, 2.123636], [41.387586, 2.123749], [41.387449, 2.123809]], {color: 'red'}).addTo(map);
L.polygon([[41.387421, 2.123695], [41.387285, 2.123756], [41.387254, 2.123635], [41.387526, 2.123512], [41.387557, 2.123634], [41.387421, 2.123695]], {color: 'red'}).addTo(map);
L.polygon([[41.387390, 2.123573], [41.387254, 2.123635], [41.387209, 2.123463], [41.387481, 2.123338], [41.387526, 2.123511], [41.387390, 2.123573]], {color: 'red'}).addTo(map);
L.polygon([[41.387346, 2.123403], [41.387225, 2.123299], [41.387289, 2.123168], [41.387531, 2.123375], [41.387467, 2.123507], [41.387346, 2.123403]], {color: 'red'}).addTo(map);
L.polygon([[41.387441, 2.123207], [41.387364, 2.123046], [41.387458, 2.122966], [41.387613, 2.123289], [41.387518, 2.123369], [41.387441, 2.123207]], {color: 'red'}).addTo(map);
L.polygon([[41.387532, 2.123130], [41.387447, 2.122975], [41.387532, 2.122894], [41.387701, 2.123204], [41.387617, 2.123285], [41.387532, 2.123130]], {color: 'red'}).addTo(map);
L.polygon([[41.387620, 2.123045], [41.387487, 2.122973], [41.387524, 2.122850], [41.387791, 2.122995], [41.387753, 2.123117], [41.387620, 2.123045]], {color: 'red'}).addTo(map);
L.polygon([[41.387660, 2.122914], [41.387526, 2.122844], [41.387567, 2.122705], [41.387835, 2.122846], [41.387794, 2.122984], [41.387660, 2.122914]], {color: 'red'}).addTo(map);
L.polygon([[41.387701, 2.122776], [41.387559, 2.122746], [41.387574, 2.122618], [41.387859, 2.122677], [41.387843, 2.122806], [41.387701, 2.122776]], {color: 'red'}).addTo(map);
L.polygon([[41.387716, 2.122649], [41.387572, 2.122654], [41.387569, 2.122515], [41.387857, 2.122505], [41.387860, 2.122644], [41.387716, 2.122649]], {color: 'red'}).addTo(map);
L.polygon([[41.387713, 2.122494], [41.387542, 2.122567], [41.387512, 2.122445], [41.387855, 2.122298], [41.387884, 2.122421], [41.387713, 2.122494]], {color: 'red'}).addTo(map);
L.polygon([[41.387678, 2.122349], [41.387541, 2.122504], [41.387474, 2.122400], [41.387748, 2.122090], [41.387815, 2.122194], [41.387678, 2.122349]], {color: 'red'}).addTo(map);
L.polygon([[41.387600, 2.122227], [41.387501, 2.122427], [41.387423, 2.122359], [41.387620, 2.121959], [41.387699, 2.122027], [41.387600, 2.122227]], {color: 'red'}).addTo(map);
L.polygon([[41.387522, 2.122159], [41.387446, 2.122376], [41.387350, 2.122316], [41.387502, 2.121883], [41.387598, 2.121942], [41.387522, 2.122159]], {color: 'red'}).addTo(map);
L.polygon([[41.387422, 2.122097], [41.387372, 2.122327], [41.387261, 2.122284], [41.387360, 2.121825], [41.387472, 2.121867], [41.387422, 2.122097]], {color: 'red'}).addTo(map);
L.polygon([[41.387307, 2.122053], [41.387276, 2.122289], [41.387180, 2.122267], [41.387241, 2.121795], [41.387338, 2.121817], [41.387307, 2.122053]], {color: 'red'}).addTo(map);
L.polygon([[41.387198, 2.122028], [41.387224, 2.122265], [41.387133, 2.122282], [41.387081, 2.121809], [41.387172, 2.121791], [41.387198, 2.122028]], {color: 'red'}).addTo(map);
L.polygon([[41.387101, 2.122047], [41.387168, 2.122269], [41.387047, 2.122333], [41.386913, 2.121889], [41.387034, 2.121825], [41.387101, 2.122047]], {color: 'red'}).addTo(map);
L.polygon([[41.386951, 2.122127], [41.387040, 2.122277], [41.386963, 2.122358], [41.386785, 2.122057], [41.386862, 2.121977], [41.386951, 2.122127]], {color: 'red'}).addTo(map);
L.polygon([[41.386871, 2.122211], [41.386981, 2.122335], [41.386892, 2.122474], [41.386672, 2.122227], [41.386761, 2.122087], [41.386871, 2.122211]], {color: 'red'}).addTo(map);
L.polygon([[41.386779, 2.122356], [41.386889, 2.122479], [41.386784, 2.122647], [41.386563, 2.122401], [41.386669, 2.122233], [41.386779, 2.122356]], {color: 'red'}).addTo(map);
L.polygon([[41.386681, 2.122512], [41.386704, 2.122603], [41.386559, 2.122667], [41.386513, 2.122486], [41.386658, 2.122421], [41.386681, 2.122512]], {color: 'red'}).addTo(map);
L.polygon([[41.386547, 2.122572], [41.386548, 2.122668], [41.386408, 2.122670], [41.386406, 2.122479], [41.386546, 2.122476], [41.386547, 2.122572]], {color: 'red'}).addTo(map);
L.polygon([[41.386417, 2.122574], [41.386409, 2.122669], [41.386284, 2.122650], [41.386301, 2.122460], [41.386425, 2.122479], [41.386417, 2.122574]], {color: 'red'}).addTo(map);
L.polygon([[41.386293, 2.122555], [41.386285, 2.122650], [41.386158, 2.122631], [41.386174, 2.122441], [41.386301, 2.122460], [41.386293, 2.122555]], {color: 'red'}).addTo(map);
L.polygon([[41.386166, 2.122536], [41.386158, 2.122631], [41.386036, 2.122612], [41.386053, 2.122422], [41.386174, 2.122441], [41.386166, 2.122536]], {color: 'red'}).addTo(map);
L.polygon([[41.386045, 2.122517], [41.386039, 2.122612], [41.385940, 2.122602], [41.385952, 2.122411], [41.386051, 2.122422], [41.386045, 2.122517]], {color: 'red'}).addTo(map);
L.polygon([[41.385944, 2.122506], [41.385939, 2.122601], [41.385834, 2.122593], [41.385843, 2.122402], [41.385949, 2.122411], [41.385944, 2.122506]], {color: 'red'}).addTo(map);
L.polygon([[41.385838, 2.122497], [41.385834, 2.122593], [41.385733, 2.122586], [41.385740, 2.122394], [41.385842, 2.122401], [41.385838, 2.122497]], {color: 'red'}).addTo(map);
L.polygon([[41.385737, 2.122490], [41.385733, 2.122586], [41.385623, 2.122578], [41.385631, 2.122386], [41.385741, 2.122394], [41.385737, 2.122490]], {color: 'red'}).addTo(map);
L.polygon([[41.385627, 2.122482], [41.385623, 2.122577], [41.385527, 2.122570], [41.385536, 2.122379], [41.385631, 2.122387], [41.385627, 2.122482]], {color: 'red'}).addTo(map);
L.polygon([[41.385505, 2.122472], [41.385499, 2.122567], [41.385400, 2.122556], [41.385412, 2.122366], [41.385511, 2.122377], [41.385505, 2.122472]], {color: 'red'}).addTo(map);
L.polygon([[41.385406, 2.122461], [41.385400, 2.122556], [41.385290, 2.122544], [41.385302, 2.122354], [41.385412, 2.122366], [41.385406, 2.122461]], {color: 'red'}).addTo(map);
L.polygon([[41.385296, 2.122449], [41.385290, 2.122544], [41.385176, 2.122532], [41.385187, 2.122342], [41.385302, 2.122354], [41.385296, 2.122449]], {color: 'red'}).addTo(map);
L.polygon([[41.385182, 2.122437], [41.385176, 2.122532], [41.385051, 2.122519], [41.385063, 2.122328], [41.385188, 2.122342], [41.385182, 2.122437]], {color: 'red'}).addTo(map);
L.polygon([[41.385060, 2.122424], [41.385054, 2.122519], [41.384959, 2.122509], [41.384970, 2.122319], [41.385066, 2.122329], [41.385060, 2.122424]], {color: 'red'}).addTo(map);
L.polygon([[41.384966, 2.122414], [41.384956, 2.122509], [41.384833, 2.122487], [41.384853, 2.122297], [41.384976, 2.122319], [41.384966, 2.122414]], {color: 'red'}).addTo(map);
L.polygon([[41.384843, 2.122392], [41.384828, 2.122486], [41.384730, 2.122458], [41.384760, 2.122271], [41.384858, 2.122298], [41.384843, 2.122392]], {color: 'red'}).addTo(map);
L.polygon([[41.384750, 2.122366], [41.384724, 2.122455], [41.384598, 2.122390], [41.384650, 2.122212], [41.384776, 2.122277], [41.384750, 2.122366]], {color: 'red'}).addTo(map);
L.polygon([[41.384616, 2.122297], [41.384621, 2.122392], [41.384488, 2.122405], [41.384478, 2.122215], [41.384611, 2.122202], [41.384616, 2.122297]], {color: 'red'}).addTo(map);
L.polygon([[41.384483, 2.122310], [41.384493, 2.122405], [41.384370, 2.122427], [41.384351, 2.122237], [41.384473, 2.122215], [41.384483, 2.122310]], {color: 'red'}).addTo(map);
L.polygon([[41.384355, 2.122333], [41.384350, 2.122380], [41.384216, 2.122354], [41.384227, 2.122259], [41.384360, 2.122286], [41.384355, 2.122333]], {color: 'red'}).addTo(map);
L.polygon([[41.384223, 2.122307], [41.384224, 2.122355], [41.384084, 2.122360], [41.384082, 2.122264], [41.384222, 2.122259], [41.384223, 2.122307]], {color: 'red'}).addTo(map);
L.polygon([[41.384082, 2.122312], [41.384083, 2.122360], [41.383947, 2.122367], [41.383944, 2.122271], [41.384081, 2.122264], [41.384082, 2.122312]], {color: 'red'}).addTo(map);
L.polygon([[41.383946, 2.122319], [41.383947, 2.122367], [41.383816, 2.122374], [41.383813, 2.122278], [41.383945, 2.122271], [41.383946, 2.122319]], {color: 'red'}).addTo(map);
L.polygon([[41.383814, 2.122326], [41.383815, 2.122374], [41.383701, 2.122378], [41.383699, 2.122282], [41.383813, 2.122278], [41.383814, 2.122326]], {color: 'red'}).addTo(map);
L.polygon([[41.383701, 2.122330], [41.383702, 2.122378], [41.383609, 2.122382], [41.383607, 2.122286], [41.383700, 2.122282], [41.383701, 2.122330]], {color: 'red'}).addTo(map);
L.polygon([[41.383609, 2.122334], [41.383610, 2.122382], [41.383505, 2.122385], [41.383503, 2.122289], [41.383608, 2.122286], [41.383609, 2.122334]], {color: 'red'}).addTo(map);
L.polygon([[41.383508, 2.122337], [41.383509, 2.122385], [41.383399, 2.122389], [41.383397, 2.122293], [41.383507, 2.122289], [41.383508, 2.122337]], {color: 'red'}).addTo(map);
L.polygon([[41.383398, 2.122341], [41.383399, 2.122389], [41.383305, 2.122392], [41.383303, 2.122296], [41.383397, 2.122293], [41.383398, 2.122341]], {color: 'red'}).addTo(map);
L.polygon([[41.383305, 2.122344], [41.383306, 2.122392], [41.383195, 2.122396], [41.383193, 2.122300], [41.383304, 2.122296], [41.383305, 2.122344]], {color: 'red'}).addTo(map);
L.polygon([[41.383194, 2.122348], [41.383195, 2.122396], [41.383091, 2.122400], [41.383089, 2.122304], [41.383193, 2.122300], [41.383194, 2.122348]], {color: 'red'}).addTo(map);
L.polygon([[41.383094, 2.122352], [41.383095, 2.122400], [41.382995, 2.122404], [41.382993, 2.122308], [41.383093, 2.122304], [41.383094, 2.122352]], {color: 'red'}).addTo(map);
L.polygon([[41.382991, 2.122356], [41.382991, 2.122404], [41.382865, 2.122406], [41.382864, 2.122310], [41.382991, 2.122308], [41.382991, 2.122356]], {color: 'red'}).addTo(map);
L.polygon([[41.382867, 2.122358], [41.382858, 2.122404], [41.382676, 2.122345], [41.382693, 2.122252], [41.382876, 2.122312], [41.382867, 2.122358]], {color: 'red'}).addTo(map);
L.polygon([[41.382600, 2.122271], [41.382553, 2.122343], [41.382480, 2.122258], [41.382574, 2.122114], [41.382647, 2.122199], [41.382600, 2.122271]], {color: 'red'}).addTo(map);
L.polygon([[41.382522, 2.122180], [41.382477, 2.122255], [41.382344, 2.122113], [41.382434, 2.121964], [41.382567, 2.122105], [41.382522, 2.122180]], {color: 'red'}).addTo(map);
L.polygon([[41.382390, 2.122039], [41.382346, 2.122114], [41.382273, 2.122039], [41.382362, 2.121889], [41.382434, 2.121964], [41.382390, 2.122039]], {color: 'red'}).addTo(map);
L.polygon([[41.382318, 2.121964], [41.382274, 2.122039], [41.382194, 2.121957], [41.382283, 2.121807], [41.382362, 2.121889], [41.382318, 2.121964]], {color: 'red'}).addTo(map);
L.polygon([[41.382239, 2.121882], [41.382194, 2.121957], [41.382076, 2.121833], [41.382165, 2.121683], [41.382284, 2.121807], [41.382239, 2.121882]], {color: 'red'}).addTo(map);
L.polygon([[41.382121, 2.121758], [41.382077, 2.121833], [41.381971, 2.121724], [41.382059, 2.121573], [41.382165, 2.121683], [41.382121, 2.121758]], {color: 'red'}).addTo(map);
L.polygon([[41.382015, 2.121648], [41.381971, 2.121723], [41.381878, 2.121627], [41.381967, 2.121477], [41.382059, 2.121573], [41.382015, 2.121648]], {color: 'red'}).addTo(map);
L.polygon([[41.381923, 2.121552], [41.381879, 2.121627], [41.381792, 2.121537], [41.381881, 2.121387], [41.381967, 2.121477], [41.381923, 2.121552]], {color: 'red'}).addTo(map);
L.polygon([[41.381837, 2.121462], [41.381793, 2.121538], [41.381706, 2.121448], [41.381794, 2.121297], [41.381881, 2.121386], [41.381837, 2.121462]], {color: 'red'}).addTo(map);
L.polygon([[41.381750, 2.121372], [41.381706, 2.121447], [41.381616, 2.121354], [41.381705, 2.121204], [41.381794, 2.121297], [41.381750, 2.121372]], {color: 'red'}).addTo(map);
L.polygon([[41.381661, 2.121279], [41.381617, 2.121354], [41.381524, 2.121258], [41.381613, 2.121108], [41.381705, 2.121204], [41.381661, 2.121279]], {color: 'red'}).addTo(map);
L.polygon([[41.381569, 2.121183], [41.381524, 2.121258], [41.381443, 2.121173], [41.381532, 2.121023], [41.381614, 2.121108], [41.381569, 2.121183]], {color: 'red'}).addTo(map);
L.polygon([[41.381487, 2.121097], [41.381443, 2.121172], [41.381369, 2.121096], [41.381458, 2.120946], [41.381531, 2.121022], [41.381487, 2.121097]], {color: 'red'}).addTo(map);
L.polygon([[41.381414, 2.121021], [41.381349, 2.121062], [41.381306, 2.120942], [41.381437, 2.120860], [41.381479, 2.120980], [41.381414, 2.121021]], {color: 'red'}).addTo(map);
L.polygon([[41.381373, 2.120905], [41.381307, 2.120944], [41.381270, 2.120834], [41.381402, 2.120756], [41.381439, 2.120866], [41.381373, 2.120905]], {color: 'red'}).addTo(map);
L.polygon([[41.381336, 2.120795], [41.381265, 2.120813], [41.381246, 2.120683], [41.381388, 2.120646], [41.381407, 2.120777], [41.381336, 2.120795]], {color: 'red'}).addTo(map);
L.polygon([[41.381318, 2.120672], [41.381246, 2.120682], [41.381234, 2.120538], [41.381378, 2.120517], [41.381390, 2.120662], [41.381318, 2.120672]], {color: 'red'}).addTo(map);
L.polygon([[41.381306, 2.120527], [41.381234, 2.120525], [41.381236, 2.120390], [41.381380, 2.120394], [41.381378, 2.120529], [41.381306, 2.120527]], {color: 'red'}).addTo(map);
L.polygon([[41.381308, 2.120395], [41.381236, 2.120390], [41.381241, 2.120252], [41.381385, 2.120261], [41.381380, 2.120400], [41.381308, 2.120395]], {color: 'red'}).addTo(map);
L.polygon([[41.381313, 2.120256], [41.381241, 2.120258], [41.381239, 2.120101], [41.381383, 2.120098], [41.381385, 2.120254], [41.381313, 2.120256]], {color: 'red'}).addTo(map);
L.polygon([[41.381311, 2.120097], [41.381239, 2.120099], [41.381237, 2.119977], [41.381381, 2.119972], [41.381383, 2.120095], [41.381311, 2.120097]], {color: 'red'}).addTo(map);
L.polygon([[41.381309, 2.119981], [41.381242, 2.120015], [41.381207, 2.119897], [41.381342, 2.119828], [41.381376, 2.119947], [41.381309, 2.119981]], {color: 'red'}).addTo(map);
L.polygon([[41.381269, 2.119843], [41.381202, 2.119877], [41.381164, 2.119746], [41.381299, 2.119678], [41.381336, 2.119809], [41.381269, 2.119843]], {color: 'red'}).addTo(map);
L.polygon([[41.381232, 2.119713], [41.381165, 2.119747], [41.381126, 2.119611], [41.381260, 2.119543], [41.381299, 2.119679], [41.381232, 2.119713]], {color: 'red'}).addTo(map);
L.polygon([[41.381193, 2.119576], [41.381126, 2.119611], [41.381087, 2.119477], [41.381221, 2.119407], [41.381260, 2.119541], [41.381193, 2.119576]], {color: 'red'}).addTo(map);
L.polygon([[41.381154, 2.119442], [41.381087, 2.119476], [41.381036, 2.119302], [41.381171, 2.119233], [41.381221, 2.119408], [41.381154, 2.119442]], {color: 'red'}).addTo(map);
L.polygon([[41.381104, 2.119268], [41.381036, 2.119301], [41.381005, 2.119186], [41.381140, 2.119120], [41.381172, 2.119235], [41.381104, 2.119268]], {color: 'red'}).addTo(map);
L.polygon([[41.381073, 2.119155], [41.381007, 2.119192], [41.380965, 2.119061], [41.381097, 2.118986], [41.381139, 2.119118], [41.381073, 2.119155]], {color: 'red'}).addTo(map);
L.polygon([[41.381030, 2.119020], [41.380964, 2.119058], [41.380926, 2.118943], [41.381059, 2.118867], [41.381096, 2.118982], [41.381030, 2.119020]], {color: 'red'}).addTo(map);
L.polygon([[41.380993, 2.118906], [41.380926, 2.118940], [41.380863, 2.118720], [41.380998, 2.118653], [41.381060, 2.118872], [41.380993, 2.118906]], {color: 'red'}).addTo(map);
L.polygon([[41.380930, 2.118682], [41.380886, 2.118757], [41.380809, 2.118678], [41.380897, 2.118527], [41.380974, 2.118607], [41.380930, 2.118682]], {color: 'red'}).addTo(map);
L.polygon([[41.380821, 2.118569], [41.380827, 2.118664], [41.380726, 2.118676], [41.380713, 2.118486], [41.380815, 2.118474], [41.380821, 2.118569]], {color: 'red'}).addTo(map);
L.polygon([[41.380676, 2.118586], [41.380726, 2.118655], [41.380652, 2.118750], [41.380552, 2.118612], [41.380626, 2.118517], [41.380676, 2.118586]], {color: 'red'}).addTo(map);
L.polygon([[41.380591, 2.118695], [41.380631, 2.118775], [41.380536, 2.118859], [41.380456, 2.118700], [41.380551, 2.118615], [41.380591, 2.118695]], {color: 'red'}).addTo(map);
L.polygon([[41.380509, 2.118768], [41.380557, 2.118896], [41.380431, 2.118980], [41.380335, 2.118723], [41.380461, 2.118640], [41.380509, 2.118768]], {color: 'red'}).addTo(map);
L.polygon([[41.380408, 2.118835], [41.380429, 2.118976], [41.380304, 2.119009], [41.380262, 2.118727], [41.380387, 2.118694], [41.380408, 2.118835]], {color: 'red'}).addTo(map);
L.polygon([[41.380284, 2.118868], [41.380306, 2.119009], [41.380161, 2.119048], [41.380118, 2.118766], [41.380262, 2.118727], [41.380284, 2.118868]], {color: 'red'}).addTo(map);
L.polygon([[41.380140, 2.118907], [41.380164, 2.119047], [41.380015, 2.119091], [41.379967, 2.118812], [41.380116, 2.118767], [41.380140, 2.118907]], {color: 'red'}).addTo(map);
L.polygon([[41.379980, 2.118955], [41.380016, 2.119090], [41.379876, 2.119156], [41.379804, 2.118886], [41.379944, 2.118820], [41.379980, 2.118955]], {color: 'red'}).addTo(map);
L.polygon([[41.379840, 2.119021], [41.379881, 2.119154], [41.379728, 2.119236], [41.379646, 2.118971], [41.379799, 2.118888], [41.379840, 2.119021]], {color: 'red'}).addTo(map);
L.polygon([[41.379681, 2.119107], [41.379726, 2.119238], [41.379569, 2.119332], [41.379479, 2.119071], [41.379636, 2.118976], [41.379681, 2.119107]], {color: 'red'}).addTo(map);
L.polygon([[41.379524, 2.119202], [41.379572, 2.119330], [41.379489, 2.119386], [41.379392, 2.119129], [41.379476, 2.119074], [41.379524, 2.119202]], {color: 'red'}).addTo(map);
L.polygon([[41.379428, 2.119266], [41.379491, 2.119383], [41.379357, 2.119510], [41.379231, 2.119276], [41.379365, 2.119149], [41.379428, 2.119266]], {color: 'red'}).addTo(map);
L.polygon([[41.379284, 2.119403], [41.379363, 2.119501], [41.379300, 2.119590], [41.379142, 2.119393], [41.379205, 2.119305], [41.379284, 2.119403]], {color: 'red'}).addTo(map);
L.polygon([[41.379212, 2.119505], [41.379295, 2.119597], [41.379183, 2.119774], [41.379017, 2.119589], [41.379129, 2.119413], [41.379212, 2.119505]], {color: 'red'}).addTo(map);
L.polygon([[41.379101, 2.119681], [41.379184, 2.119773], [41.379123, 2.119870], [41.378957, 2.119687], [41.379018, 2.119589], [41.379101, 2.119681]], {color: 'red'}).addTo(map);
L.polygon([[41.379040, 2.119779], [41.379128, 2.119861], [41.379073, 2.119966], [41.378896, 2.119801], [41.378952, 2.119697], [41.379040, 2.119779]], {color: 'red'}).addTo(map);
L.polygon([[41.378981, 2.119891], [41.379071, 2.119969], [41.379019, 2.120075], [41.378838, 2.119918], [41.378891, 2.119813], [41.378981, 2.119891]], {color: 'red'}).addTo(map);
L.polygon([[41.378929, 2.119997], [41.379019, 2.120077], [41.378960, 2.120193], [41.378780, 2.120033], [41.378839, 2.119917], [41.378929, 2.119997]], {color: 'red'}).addTo(map);
L.polygon([[41.378870, 2.120114], [41.378960, 2.120192], [41.378910, 2.120295], [41.378729, 2.120138], [41.378780, 2.120036], [41.378870, 2.120114]], {color: 'red'}).addTo(map);
L.polygon([[41.378820, 2.120216], [41.378910, 2.120296], [41.378809, 2.120496], [41.378629, 2.120336], [41.378730, 2.120136], [41.378820, 2.120216]], {color: 'red'}).addTo(map);
L.polygon([[41.378720, 2.120415], [41.378807, 2.120500], [41.378706, 2.120680], [41.378533, 2.120510], [41.378633, 2.120330], [41.378720, 2.120415]], {color: 'red'}).addTo(map);
L.polygon([[41.378627, 2.120582], [41.378699, 2.120689], [41.378595, 2.120811], [41.378452, 2.120597], [41.378555, 2.120475], [41.378627, 2.120582]], {color: 'red'}).addTo(map);
L.polygon([[41.378524, 2.120704], [41.378596, 2.120811], [41.378500, 2.120924], [41.378357, 2.120710], [41.378452, 2.120597], [41.378524, 2.120704]], {color: 'red'}).addTo(map);
L.polygon([[41.378428, 2.120818], [41.378500, 2.120925], [41.378429, 2.121010], [41.378284, 2.120797], [41.378356, 2.120711], [41.378428, 2.120818]], {color: 'red'}).addTo(map);
L.polygon([[41.378357, 2.120903], [41.378429, 2.121010], [41.378354, 2.121099], [41.378210, 2.120885], [41.378285, 2.120796], [41.378357, 2.120903]], {color: 'red'}).addTo(map);
L.polygon([[41.378282, 2.120992], [41.378349, 2.121104], [41.378246, 2.121213], [41.378112, 2.120989], [41.378215, 2.120880], [41.378282, 2.120992]], {color: 'red'}).addTo(map);
L.polygon([[41.378186, 2.121094], [41.378242, 2.121217], [41.378101, 2.121329], [41.377990, 2.121083], [41.378130, 2.120971], [41.378186, 2.121094]], {color: 'red'}).addTo(map);
L.polygon([[41.378046, 2.121206], [41.378101, 2.121329], [41.377947, 2.121451], [41.377837, 2.121204], [41.377991, 2.121083], [41.378046, 2.121206]], {color: 'red'}).addTo(map);
L.polygon([[41.377897, 2.121324], [41.377949, 2.121502], [41.377784, 2.121587], [41.377680, 2.121230], [41.377845, 2.121146], [41.377897, 2.121324]], {color: 'red'}).addTo(map);
L.polygon([[41.377734, 2.121408], [41.377789, 2.121585], [41.377699, 2.121634], [41.377589, 2.121280], [41.377679, 2.121231], [41.377734, 2.121408]], {color: 'red'}).addTo(map);
L.polygon([[41.377643, 2.121458], [41.377701, 2.121633], [41.377604, 2.121690], [41.377488, 2.121339], [41.377585, 2.121283], [41.377643, 2.121458]], {color: 'red'}).addTo(map);
L.polygon([[41.377547, 2.121514], [41.377576, 2.121701], [41.377484, 2.121726], [41.377427, 2.121351], [41.377518, 2.121327], [41.377547, 2.121514]], {color: 'red'}).addTo(map);
L.polygon([[41.377462, 2.121537], [41.377491, 2.121724], [41.377386, 2.121753], [41.377328, 2.121379], [41.377433, 2.121350], [41.377462, 2.121537]], {color: 'red'}).addTo(map);
L.polygon([[41.377350, 2.121568], [41.377349, 2.121759], [41.377251, 2.121758], [41.377254, 2.121376], [41.377351, 2.121377], [41.377350, 2.121568]], {color: 'red'}).addTo(map);
L.polygon([[41.377265, 2.121567], [41.377265, 2.121758], [41.377169, 2.121758], [41.377169, 2.121376], [41.377265, 2.121376], [41.377265, 2.121567]], {color: 'red'}).addTo(map);
L.polygon([[41.377169, 2.121567], [41.377168, 2.121758], [41.377066, 2.121757], [41.377068, 2.121375], [41.377170, 2.121376], [41.377169, 2.121567]], {color: 'red'}).addTo(map);
L.polygon([[41.376997, 2.121565], [41.376994, 2.121756], [41.376892, 2.121753], [41.376898, 2.121371], [41.377000, 2.121374], [41.376997, 2.121565]], {color: 'red'}).addTo(map);
L.polygon([[41.376896, 2.121562], [41.376894, 2.121753], [41.376721, 2.121749], [41.376725, 2.121367], [41.376898, 2.121371], [41.376896, 2.121562]], {color: 'red'}).addTo(map);
L.polygon([[41.376723, 2.121558], [41.376718, 2.121749], [41.376601, 2.121743], [41.376612, 2.121361], [41.376728, 2.121367], [41.376723, 2.121558]], {color: 'red'}).addTo(map);
L.polygon([[41.376605, 2.121552], [41.376601, 2.121743], [41.376511, 2.121740], [41.376518, 2.121358], [41.376609, 2.121361], [41.376605, 2.121552]], {color: 'red'}).addTo(map);
L.polygon([[41.376518, 2.121549], [41.376514, 2.121740], [41.376397, 2.121736], [41.376404, 2.121354], [41.376522, 2.121358], [41.376518, 2.121549]], {color: 'red'}).addTo(map);
L.polygon([[41.376399, 2.121545], [41.376400, 2.121736], [41.376252, 2.121738], [41.376249, 2.121356], [41.376398, 2.121354], [41.376399, 2.121545]], {color: 'red'}).addTo(map);
L.polygon([[41.376249, 2.121547], [41.376252, 2.121690], [41.376112, 2.121695], [41.376106, 2.121408], [41.376246, 2.121404], [41.376249, 2.121547]], {color: 'red'}).addTo(map);
L.polygon([[41.376106, 2.121552], [41.376125, 2.121693], [41.375988, 2.121725], [41.375950, 2.121442], [41.376087, 2.121411], [41.376106, 2.121552]], {color: 'red'}).addTo(map);
L.polygon([[41.375960, 2.121586], [41.375999, 2.121720], [41.375886, 2.121778], [41.375808, 2.121510], [41.375921, 2.121452], [41.375960, 2.121586]], {color: 'red'}).addTo(map);
L.polygon([[41.375839, 2.121648], [41.375872, 2.121785], [41.375768, 2.121828], [41.375703, 2.121555], [41.375806, 2.121511], [41.375839, 2.121648]], {color: 'red'}).addTo(map);
L.polygon([[41.375747, 2.121687], [41.375846, 2.121745], [41.375802, 2.121879], [41.375604, 2.121764], [41.375648, 2.121629], [41.375747, 2.121687]], {color: 'red'}).addTo(map);
L.polygon([[41.375675, 2.121906], [41.375819, 2.121908], [41.375818, 2.122047], [41.375530, 2.122044], [41.375531, 2.121904], [41.375675, 2.121906]], {color: 'red'}).addTo(map);
L.polygon([[41.375674, 2.122044], [41.375818, 2.122046], [41.375817, 2.122208], [41.375529, 2.122205], [41.375530, 2.122042], [41.375674, 2.122044]], {color: 'red'}).addTo(map);
L.polygon([[41.375673, 2.122207], [41.375817, 2.122209], [41.375816, 2.122335], [41.375528, 2.122331], [41.375529, 2.122205], [41.375673, 2.122207]], {color: 'red'}).addTo(map);
L.polygon([[41.375672, 2.122333], [41.375816, 2.122335], [41.375815, 2.122463], [41.375527, 2.122459], [41.375528, 2.122331], [41.375672, 2.122333]], {color: 'red'}).addTo(map);
L.polygon([[41.375671, 2.122462], [41.375815, 2.122467], [41.375812, 2.122625], [41.375524, 2.122616], [41.375527, 2.122457], [41.375671, 2.122462]], {color: 'red'}).addTo(map);
L.polygon([[41.375668, 2.122620], [41.375812, 2.122626], [41.375808, 2.122801], [41.375520, 2.122789], [41.375524, 2.122614], [41.375668, 2.122620]], {color: 'red'}).addTo(map);
L.polygon([[41.375664, 2.122796], [41.375808, 2.122802], [41.375803, 2.123015], [41.375515, 2.123003], [41.375520, 2.122790], [41.375664, 2.122796]], {color: 'red'}).addTo(map);
L.polygon([[41.375659, 2.123009], [41.375803, 2.123017], [41.375797, 2.123198], [41.375509, 2.123182], [41.375515, 2.123001], [41.375659, 2.123009]], {color: 'red'}).addTo(map);
L.polygon([[41.375653, 2.123190], [41.375797, 2.123196], [41.375793, 2.123368], [41.375505, 2.123357], [41.375509, 2.123184], [41.375653, 2.123190]], {color: 'red'}).addTo(map);
L.polygon([[41.375649, 2.123363], [41.375757, 2.123368], [41.375753, 2.123519], [41.375537, 2.123509], [41.375541, 2.123358], [41.375649, 2.123363]], {color: 'red'}).addTo(map);
L.polygon([[41.375645, 2.123514], [41.375753, 2.123518], [41.375749, 2.123727], [41.375533, 2.123720], [41.375537, 2.123510], [41.375645, 2.123514]], {color: 'red'}).addTo(map);
L.polygon([[41.375641, 2.123716], [41.375749, 2.123721], [41.375744, 2.123909], [41.375528, 2.123899], [41.375533, 2.123711], [41.375641, 2.123716]], {color: 'red'}).addTo(map);
L.polygon([[41.375636, 2.123894], [41.375744, 2.123898], [41.375740, 2.124102], [41.375524, 2.124094], [41.375528, 2.123890], [41.375636, 2.123894]], {color: 'red'}).addTo(map);
L.polygon([[41.375632, 2.124095], [41.375740, 2.124099], [41.375736, 2.124292], [41.375520, 2.124284], [41.375524, 2.124091], [41.375632, 2.124095]], {color: 'red'}).addTo(map);
L.polygon([[41.375628, 2.124293], [41.375736, 2.124298], [41.375731, 2.124493], [41.375515, 2.124484], [41.375520, 2.124288], [41.375628, 2.124293]], {color: 'red'}).addTo(map);
L.polygon([[41.375623, 2.124489], [41.375731, 2.124493], [41.375728, 2.124650], [41.375512, 2.124643], [41.375515, 2.124485], [41.375623, 2.124489]], {color: 'red'}).addTo(map);
L.polygon([[41.375620, 2.124647], [41.375728, 2.124651], [41.375725, 2.124781], [41.375509, 2.124772], [41.375512, 2.124643], [41.375620, 2.124647]], {color: 'red'}).addTo(map);
L.polygon([[41.375617, 2.124776], [41.375725, 2.124780], [41.375722, 2.124909], [41.375506, 2.124901], [41.375509, 2.124772], [41.375617, 2.124776]], {color: 'red'}).addTo(map);
L.polygon([[41.375614, 2.124905], [41.375722, 2.124908], [41.375720, 2.125028], [41.375504, 2.125022], [41.375506, 2.124902], [41.375614, 2.124905]], {color: 'red'}).addTo(map);
L.polygon([[41.375612, 2.125026], [41.375720, 2.125030], [41.375716, 2.125206], [41.375500, 2.125198], [41.375504, 2.125022], [41.375612, 2.125026]], {color: 'red'}).addTo(map);
L.polygon([[41.375608, 2.125202], [41.375716, 2.125206], [41.375712, 2.125391], [41.375496, 2.125383], [41.375500, 2.125198], [41.375608, 2.125202]], {color: 'red'}).addTo(map);
L.polygon([[41.375604, 2.125384], [41.375676, 2.125385], [41.375675, 2.125567], [41.375531, 2.125566], [41.375532, 2.125383], [41.375604, 2.125384]], {color: 'red'}).addTo(map);
L.polygon([[41.375603, 2.125570], [41.375675, 2.125571], [41.375674, 2.125744], [41.375530, 2.125742], [41.375531, 2.125569], [41.375603, 2.125570]], {color: 'red'}).addTo(map);
L.polygon([[41.375602, 2.125743], [41.375674, 2.125744], [41.375673, 2.125940], [41.375529, 2.125939], [41.375530, 2.125742], [41.375602, 2.125743]], {color: 'red'}).addTo(map);
L.polygon([[41.375601, 2.125940], [41.375673, 2.125941], [41.375671, 2.126130], [41.375527, 2.126127], [41.375529, 2.125939], [41.375601, 2.125940]], {color: 'red'}).addTo(map);
L.polygon([[41.375599, 2.126129], [41.375671, 2.126130], [41.375670, 2.126325], [41.375526, 2.126323], [41.375527, 2.126128], [41.375599, 2.126129]], {color: 'red'}).addTo(map);
L.polygon([[41.375598, 2.126324], [41.375670, 2.126327], [41.375665, 2.126529], [41.375521, 2.126522], [41.375526, 2.126321], [41.375598, 2.126324]], {color: 'red'}).addTo(map);
L.polygon([[41.375593, 2.126527], [41.375665, 2.126530], [41.375660, 2.126732], [41.375516, 2.126725], [41.375521, 2.126524], [41.375593, 2.126527]], {color: 'red'}).addTo(map);
L.polygon([[41.375588, 2.126729], [41.375660, 2.126732], [41.375655, 2.126942], [41.375511, 2.126936], [41.375516, 2.126726], [41.375588, 2.126729]], {color: 'red'}).addTo(map);
L.polygon([[41.375583, 2.126939], [41.375655, 2.126942], [41.375650, 2.127160], [41.375506, 2.127154], [41.375511, 2.126936], [41.375583, 2.126939]], {color: 'red'}).addTo(map);
L.polygon([[41.375575, 2.127259], [41.375647, 2.127263], [41.375641, 2.127469], [41.375497, 2.127462], [41.375503, 2.127255], [41.375575, 2.127259]], {color: 'red'}).addTo(map);
L.polygon([[41.375569, 2.127466], [41.375641, 2.127468], [41.375638, 2.127673], [41.375494, 2.127669], [41.375497, 2.127464], [41.375569, 2.127466]], {color: 'red'}).addTo(map);
L.polygon([[41.375566, 2.127672], [41.375638, 2.127674], [41.375635, 2.127863], [41.375491, 2.127859], [41.375494, 2.127670], [41.375566, 2.127672]], {color: 'red'}).addTo(map);
L.polygon([[41.375563, 2.127861], [41.375635, 2.127863], [41.375632, 2.128072], [41.375488, 2.128068], [41.375491, 2.127859], [41.375563, 2.127861]], {color: 'red'}).addTo(map);
L.polygon([[41.375560, 2.128071], [41.375632, 2.128073], [41.375628, 2.128285], [41.375484, 2.128280], [41.375488, 2.128069], [41.375560, 2.128071]], {color: 'red'}).addTo(map);
L.polygon([[41.375556, 2.128283], [41.375628, 2.128286], [41.375623, 2.128518], [41.375479, 2.128513], [41.375484, 2.128280], [41.375556, 2.128283]], {color: 'red'}).addTo(map);
L.polygon([[41.375551, 2.128516], [41.375623, 2.128519], [41.375618, 2.128734], [41.375474, 2.128728], [41.375479, 2.128513], [41.375551, 2.128516]], {color: 'red'}).addTo(map);
L.polygon([[41.375546, 2.128732], [41.375618, 2.128734], [41.375614, 2.128942], [41.375470, 2.128937], [41.375474, 2.128730], [41.375546, 2.128732]], {color: 'red'}).addTo(map);
L.polygon([[41.375542, 2.128940], [41.375614, 2.128943], [41.375610, 2.129130], [41.375466, 2.129125], [41.375470, 2.128937], [41.375542, 2.128940]], {color: 'red'}).addTo(map);
L.polygon([[41.375538, 2.129128], [41.375610, 2.129131], [41.375607, 2.129252], [41.375463, 2.129246], [41.375466, 2.129125], [41.375538, 2.129128]], {color: 'red'}).addTo(map);
L.polygon([[41.375535, 2.129250], [41.375607, 2.129254], [41.375601, 2.129452], [41.375457, 2.129444], [41.375463, 2.129246], [41.375535, 2.129250]], {color: 'red'}).addTo(map);
L.polygon([[41.375529, 2.129448], [41.375601, 2.129451], [41.375597, 2.129608], [41.375453, 2.129601], [41.375457, 2.129445], [41.375529, 2.129448]], {color: 'red'}).addTo(map);
L.polygon([[41.375525, 2.129605], [41.375597, 2.129610], [41.375592, 2.129737], [41.375448, 2.129727], [41.375453, 2.129600], [41.375525, 2.129605]], {color: 'red'}).addTo(map);
L.polygon([[41.375520, 2.129732], [41.375592, 2.129736], [41.375588, 2.129869], [41.375444, 2.129862], [41.375448, 2.129728], [41.375520, 2.129732]], {color: 'red'}).addTo(map);
L.polygon([[41.375516, 2.129866], [41.375588, 2.129870], [41.375583, 2.130030], [41.375439, 2.130022], [41.375444, 2.129862], [41.375516, 2.129866]], {color: 'red'}).addTo(map);
L.polygon([[41.375511, 2.130027], [41.375583, 2.130028], [41.375582, 2.130204], [41.375438, 2.130203], [41.375439, 2.130026], [41.375511, 2.130027]], {color: 'red'}).addTo(map);
L.polygon([[41.375510, 2.130204], [41.375582, 2.130205], [41.375581, 2.130341], [41.375437, 2.130339], [41.375438, 2.130203], [41.375510, 2.130204]], {color: 'red'}).addTo(map);
L.polygon([[41.375509, 2.130340], [41.375581, 2.130340], [41.375581, 2.130462], [41.375437, 2.130462], [41.375437, 2.130340], [41.375509, 2.130340]], {color: 'red'}).addTo(map);
L.polygon([[41.375509, 2.130452], [41.375581, 2.130453], [41.375579, 2.130627], [41.375435, 2.130624], [41.375437, 2.130451], [41.375509, 2.130452]], {color: 'red'}).addTo(map);
L.polygon([[41.375507, 2.130637], [41.375578, 2.130651], [41.375560, 2.130819], [41.375418, 2.130792], [41.375436, 2.130623], [41.375507, 2.130637]], {color: 'red'}).addTo(map);
L.polygon([[41.375489, 2.130805], [41.375561, 2.130812], [41.375550, 2.131023], [41.375406, 2.131010], [41.375417, 2.130798], [41.375489, 2.130805]], {color: 'red'}).addTo(map);
L.polygon([[41.375478, 2.131017], [41.375550, 2.131020], [41.375546, 2.131217], [41.375402, 2.131212], [41.375406, 2.131014], [41.375478, 2.131017]], {color: 'red'}).addTo(map);
L.polygon([[41.375474, 2.131217], [41.375546, 2.131219], [41.375543, 2.131414], [41.375399, 2.131410], [41.375402, 2.131215], [41.375474, 2.131217]], {color: 'red'}).addTo(map);
L.polygon([[41.375471, 2.131411], [41.375543, 2.131418], [41.375536, 2.131546], [41.375392, 2.131532], [41.375399, 2.131404], [41.375471, 2.131411]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375565, 2.131353], [41.375565, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375559, 2.131353], [41.375559, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375572, 2.131353], [41.375572, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375566, 2.131353], [41.375566, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375565, 2.131353], [41.375565, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375559, 2.131353], [41.375559, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375572, 2.131353], [41.375572, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375566, 2.131353], [41.375566, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375565, 2.131353], [41.375565, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375559, 2.131353], [41.375559, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375572, 2.131353], [41.375572, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375566, 2.131353], [41.375566, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375565, 2.131353], [41.375565, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375559, 2.131353], [41.375559, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375469, 2.131353], [41.375572, 2.131353], [41.375572, 2.131545], [41.375469, 2.131545], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375469, 2.131449], [41.375541, 2.131452], [41.375538, 2.131593], [41.375394, 2.131587], [41.375397, 2.131446], [41.375469, 2.131449]], {color: 'red'}).addTo(map);
L.polygon([[41.375464, 2.131656], [41.375465, 2.131752], [41.375374, 2.131753], [41.375373, 2.131561], [41.375463, 2.131560], [41.375464, 2.131656]], {color: 'red'}).addTo(map);
L.polygon([[41.375018, 2.131661], [41.375022, 2.131756], [41.374924, 2.131763], [41.374917, 2.131572], [41.375014, 2.131566], [41.375018, 2.131661]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374802, 2.131635], [41.374802, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374796, 2.131635], [41.374796, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374812, 2.131635], [41.374812, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374809, 2.131635], [41.374809, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374804, 2.131635], [41.374804, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374803, 2.131635], [41.374803, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374802, 2.131635], [41.374802, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374796, 2.131635], [41.374796, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374812, 2.131635], [41.374812, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374809, 2.131635], [41.374809, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374804, 2.131635], [41.374804, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374803, 2.131635], [41.374803, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374802, 2.131635], [41.374802, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374796, 2.131635], [41.374796, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374812, 2.131635], [41.374812, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374809, 2.131635], [41.374809, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374804, 2.131635], [41.374804, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374803, 2.131635], [41.374803, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374802, 2.131635], [41.374802, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374796, 2.131635], [41.374796, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374812, 2.131635], [41.374812, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374809, 2.131635], [41.374809, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374804, 2.131635], [41.374804, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374803, 2.131635], [41.374803, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374802, 2.131635], [41.374802, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374796, 2.131635], [41.374796, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374812, 2.131635], [41.374812, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374809, 2.131635], [41.374809, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374804, 2.131635], [41.374804, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374803, 2.131635], [41.374803, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374802, 2.131635], [41.374802, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374796, 2.131635], [41.374796, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374812, 2.131635], [41.374812, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374809, 2.131635], [41.374809, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374705, 2.131635], [41.374804, 2.131635], [41.374804, 2.131731], [41.374705, 2.131731], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374705, 2.131683], [41.374740, 2.131673], [41.374763, 2.131823], [41.374693, 2.131842], [41.374670, 2.131693], [41.374705, 2.131683]], {color: 'red'}).addTo(map);
L.polygon([[41.374715, 2.131748], [41.374722, 2.131795], [41.374628, 2.131820], [41.374614, 2.131727], [41.374708, 2.131701], [41.374715, 2.131748]], {color: 'red'}).addTo(map);
L.polygon([[41.374230, 2.131879], [41.374224, 2.131926], [41.374122, 2.131905], [41.374133, 2.131810], [41.374236, 2.131832], [41.374230, 2.131879]], {color: 'red'}).addTo(map);
L.polygon([[41.374098, 2.131851], [41.374063, 2.131861], [41.374039, 2.131717], [41.374109, 2.131696], [41.374133, 2.131841], [41.374098, 2.131851]], {color: 'red'}).addTo(map);
L.polygon([[41.374074, 2.131708], [41.374039, 2.131718], [41.374018, 2.131589], [41.374088, 2.131569], [41.374109, 2.131698], [41.374074, 2.131708]], {color: 'red'}).addTo(map);
L.polygon([[41.374053, 2.131579], [41.374018, 2.131589], [41.373998, 2.131470], [41.374068, 2.131449], [41.374088, 2.131569], [41.374053, 2.131579]], {color: 'red'}).addTo(map);
L.polygon([[41.374033, 2.131459], [41.373998, 2.131469], [41.373975, 2.131323], [41.374045, 2.131304], [41.374068, 2.131449], [41.374033, 2.131459]], {color: 'red'}).addTo(map);
L.polygon([[41.374010, 2.131314], [41.373975, 2.131324], [41.373953, 2.131192], [41.374023, 2.131171], [41.374045, 2.131304], [41.374010, 2.131314]], {color: 'red'}).addTo(map);
L.polygon([[41.373988, 2.131182], [41.373973, 2.131138], [41.374108, 2.131059], [41.374138, 2.131146], [41.374003, 2.131226], [41.373988, 2.131182]], {color: 'red'}).addTo(map);
L.polygon([[41.374219, 2.131045], [41.374209, 2.130999], [41.374326, 2.130953], [41.374346, 2.131045], [41.374229, 2.131091], [41.374219, 2.131045]], {color: 'red'}).addTo(map);
L.polygon([[41.374336, 2.130999], [41.374326, 2.130953], [41.374441, 2.130909], [41.374461, 2.131001], [41.374346, 2.131045], [41.374336, 2.130999]], {color: 'red'}).addTo(map);
L.polygon([[41.374451, 2.130955], [41.374441, 2.130909], [41.374553, 2.130866], [41.374573, 2.130958], [41.374461, 2.131001], [41.374451, 2.130955]], {color: 'red'}).addTo(map);
L.polygon([[41.374562, 2.130912], [41.374554, 2.130865], [41.374672, 2.130829], [41.374689, 2.130922], [41.374570, 2.130959], [41.374562, 2.130912]], {color: 'red'}).addTo(map);
L.polygon([[41.374684, 2.130874], [41.374677, 2.130827], [41.374805, 2.130791], [41.374820, 2.130885], [41.374691, 2.130921], [41.374684, 2.130874]], {color: 'red'}).addTo(map);
L.polygon([[41.374812, 2.130838], [41.374804, 2.130791], [41.374909, 2.130761], [41.374924, 2.130855], [41.374820, 2.130885], [41.374812, 2.130838]], {color: 'red'}).addTo(map);
L.polygon([[41.374917, 2.130808], [41.374909, 2.130761], [41.375012, 2.130731], [41.375028, 2.130825], [41.374925, 2.130855], [41.374917, 2.130808]], {color: 'red'}).addTo(map);
L.polygon([[41.375019, 2.130778], [41.375011, 2.130731], [41.375107, 2.130704], [41.375122, 2.130798], [41.375027, 2.130825], [41.375019, 2.130778]], {color: 'red'}).addTo(map);
L.polygon([[41.375114, 2.130751], [41.375106, 2.130704], [41.375212, 2.130674], [41.375227, 2.130768], [41.375122, 2.130798], [41.375114, 2.130751]], {color: 'red'}).addTo(map);
L.polygon([[41.375219, 2.130721], [41.375212, 2.130674], [41.375311, 2.130646], [41.375326, 2.130740], [41.375226, 2.130768], [41.375219, 2.130721]], {color: 'red'}).addTo(map);
L.polygon([[41.375319, 2.130693], [41.375312, 2.130646], [41.375410, 2.130620], [41.375424, 2.130713], [41.375326, 2.130740], [41.375319, 2.130693]], {color: 'red'}).addTo(map);
L.polygon([[41.375418, 2.130666], [41.375414, 2.130619], [41.375537, 2.130599], [41.375545, 2.130694], [41.375422, 2.130713], [41.375418, 2.130666]], {color: 'red'}).addTo(map);
L.polygon([[41.375507, 2.130652], [41.375471, 2.130652], [41.375471, 2.130532], [41.375543, 2.130532], [41.375543, 2.130652], [41.375507, 2.130652]], {color: 'red'}).addTo(map);
L.polygon([[41.375507, 2.130572], [41.375435, 2.130570], [41.375437, 2.130439], [41.375581, 2.130443], [41.375579, 2.130574], [41.375507, 2.130572]], {color: 'red'}).addTo(map);
L.polygon([[41.375509, 2.130443], [41.375437, 2.130443], [41.375437, 2.130267], [41.375581, 2.130267], [41.375581, 2.130443], [41.375509, 2.130443]], {color: 'red'}).addTo(map);
L.polygon([[41.375509, 2.130268], [41.375437, 2.130267], [41.375439, 2.130090], [41.375583, 2.130093], [41.375581, 2.130269], [41.375509, 2.130268]], {color: 'red'}).addTo(map);
L.polygon([[41.375511, 2.130089], [41.375439, 2.130086], [41.375444, 2.129864], [41.375588, 2.129870], [41.375583, 2.130092], [41.375511, 2.130089]], {color: 'red'}).addTo(map);
L.polygon([[41.375516, 2.129864], [41.375444, 2.129860], [41.375451, 2.129651], [41.375595, 2.129659], [41.375588, 2.129868], [41.375516, 2.129864]], {color: 'red'}).addTo(map);
L.polygon([[41.375523, 2.129654], [41.375451, 2.129650], [41.375457, 2.129451], [41.375601, 2.129458], [41.375595, 2.129658], [41.375523, 2.129654]], {color: 'red'}).addTo(map);
L.polygon([[41.375529, 2.129454], [41.375457, 2.129450], [41.375461, 2.129323], [41.375605, 2.129331], [41.375601, 2.129458], [41.375529, 2.129454]], {color: 'red'}).addTo(map);
L.polygon([[41.375533, 2.129332], [41.375461, 2.129329], [41.375466, 2.129138], [41.375610, 2.129145], [41.375605, 2.129335], [41.375533, 2.129332]], {color: 'red'}).addTo(map);
L.polygon([[41.375538, 2.129137], [41.375466, 2.129134], [41.375470, 2.128941], [41.375614, 2.128946], [41.375610, 2.129140], [41.375538, 2.129137]], {color: 'red'}).addTo(map);
L.polygon([[41.375542, 2.128943], [41.375470, 2.128940], [41.375474, 2.128747], [41.375618, 2.128752], [41.375614, 2.128946], [41.375542, 2.128943]], {color: 'red'}).addTo(map);
L.polygon([[41.375546, 2.128748], [41.375474, 2.128745], [41.375479, 2.128534], [41.375623, 2.128540], [41.375618, 2.128751], [41.375546, 2.128748]], {color: 'red'}).addTo(map);
L.polygon([[41.375551, 2.128537], [41.375479, 2.128534], [41.375483, 2.128334], [41.375627, 2.128339], [41.375623, 2.128540], [41.375551, 2.128537]], {color: 'red'}).addTo(map);
L.polygon([[41.375555, 2.128336], [41.375483, 2.128333], [41.375487, 2.128128], [41.375631, 2.128133], [41.375627, 2.128339], [41.375555, 2.128336]], {color: 'red'}).addTo(map);
L.polygon([[41.375559, 2.128136], [41.375487, 2.128134], [41.375490, 2.127942], [41.375634, 2.127946], [41.375631, 2.128138], [41.375559, 2.128136]], {color: 'red'}).addTo(map);
L.polygon([[41.375562, 2.127938], [41.375490, 2.127936], [41.375494, 2.127689], [41.375638, 2.127693], [41.375634, 2.127940], [41.375562, 2.127938]], {color: 'red'}).addTo(map);
L.polygon([[41.375566, 2.127690], [41.375494, 2.127689], [41.375496, 2.127511], [41.375640, 2.127513], [41.375638, 2.127691], [41.375566, 2.127690]], {color: 'red'}).addTo(map);
L.polygon([[41.375568, 2.127512], [41.375496, 2.127509], [41.375500, 2.127339], [41.375644, 2.127345], [41.375640, 2.127515], [41.375568, 2.127512]], {color: 'red'}).addTo(map);
L.polygon([[41.375572, 2.127338], [41.375518, 2.127275], [41.375582, 2.127179], [41.375690, 2.127306], [41.375626, 2.127401], [41.375572, 2.127338]], {color: 'red'}).addTo(map);
L.polygon([[41.375666, 2.127196], [41.375654, 2.127151], [41.375744, 2.127109], [41.375768, 2.127199], [41.375678, 2.127241], [41.375666, 2.127196]], {color: 'red'}).addTo(map);
L.polygon([[41.375756, 2.127154], [41.375744, 2.127109], [41.375879, 2.127047], [41.375902, 2.127137], [41.375768, 2.127199], [41.375756, 2.127154]], {color: 'red'}).addTo(map);
L.polygon([[41.375890, 2.127092], [41.375878, 2.127047], [41.375986, 2.126997], [41.376009, 2.127088], [41.375902, 2.127137], [41.375890, 2.127092]], {color: 'red'}).addTo(map);
L.polygon([[41.375998, 2.127042], [41.375986, 2.126997], [41.376085, 2.126952], [41.376108, 2.127042], [41.376010, 2.127087], [41.375998, 2.127042]], {color: 'red'}).addTo(map);
L.polygon([[41.376096, 2.126997], [41.376084, 2.126952], [41.376171, 2.126912], [41.376195, 2.127002], [41.376108, 2.127042], [41.376096, 2.126997]], {color: 'red'}).addTo(map);
L.polygon([[41.376183, 2.126957], [41.376171, 2.126912], [41.376282, 2.126862], [41.376305, 2.126952], [41.376195, 2.127002], [41.376183, 2.126957]], {color: 'red'}).addTo(map);
L.polygon([[41.376293, 2.126907], [41.376281, 2.126862], [41.376379, 2.126817], [41.376403, 2.126907], [41.376305, 2.126952], [41.376293, 2.126907]], {color: 'red'}).addTo(map);
L.polygon([[41.376390, 2.126862], [41.376378, 2.126817], [41.376497, 2.126761], [41.376521, 2.126851], [41.376402, 2.126907], [41.376390, 2.126862]], {color: 'red'}).addTo(map);
L.polygon([[41.376509, 2.126806], [41.376497, 2.126761], [41.376594, 2.126716], [41.376618, 2.126806], [41.376521, 2.126851], [41.376509, 2.126806]], {color: 'red'}).addTo(map);
L.polygon([[41.376606, 2.126761], [41.376595, 2.126716], [41.376687, 2.126675], [41.376710, 2.126766], [41.376617, 2.126806], [41.376606, 2.126761]], {color: 'red'}).addTo(map);
L.polygon([[41.376696, 2.126721], [41.376685, 2.126676], [41.376778, 2.126634], [41.376801, 2.126724], [41.376707, 2.126766], [41.376696, 2.126721]], {color: 'red'}).addTo(map);
L.polygon([[41.376792, 2.126678], [41.376781, 2.126632], [41.376879, 2.126591], [41.376901, 2.126682], [41.376803, 2.126724], [41.376792, 2.126678]], {color: 'red'}).addTo(map);
L.polygon([[41.376890, 2.126636], [41.376879, 2.126590], [41.376971, 2.126551], [41.376993, 2.126642], [41.376901, 2.126682], [41.376890, 2.126636]], {color: 'red'}).addTo(map);
L.polygon([[41.376981, 2.126597], [41.376970, 2.126551], [41.377056, 2.126515], [41.377078, 2.126606], [41.376992, 2.126643], [41.376981, 2.126597]], {color: 'red'}).addTo(map);
L.polygon([[41.377067, 2.126560], [41.377056, 2.126514], [41.377167, 2.126468], [41.377189, 2.126559], [41.377078, 2.126606], [41.377067, 2.126560]], {color: 'red'}).addTo(map);
L.polygon([[41.377178, 2.126513], [41.377167, 2.126468], [41.377257, 2.126428], [41.377280, 2.126519], [41.377189, 2.126558], [41.377178, 2.126513]], {color: 'red'}).addTo(map);
L.polygon([[41.377268, 2.126474], [41.377256, 2.126429], [41.377355, 2.126383], [41.377379, 2.126473], [41.377280, 2.126519], [41.377268, 2.126474]], {color: 'red'}).addTo(map);
L.polygon([[41.377367, 2.126428], [41.377355, 2.126383], [41.377462, 2.126330], [41.377487, 2.126420], [41.377379, 2.126473], [41.377367, 2.126428]], {color: 'red'}).addTo(map);
L.polygon([[41.377475, 2.126375], [41.377462, 2.126330], [41.377558, 2.126282], [41.377584, 2.126371], [41.377488, 2.126420], [41.377475, 2.126375]], {color: 'red'}).addTo(map);
L.polygon([[41.377570, 2.126327], [41.377557, 2.126282], [41.377646, 2.126238], [41.377671, 2.126328], [41.377583, 2.126372], [41.377570, 2.126327]], {color: 'red'}).addTo(map);
L.polygon([[41.377659, 2.126283], [41.377646, 2.126238], [41.377746, 2.126188], [41.377771, 2.126278], [41.377672, 2.126328], [41.377659, 2.126283]], {color: 'red'}).addTo(map);
L.polygon([[41.377748, 2.126238], [41.377775, 2.126206], [41.377858, 2.126330], [41.377804, 2.126394], [41.377721, 2.126270], [41.377748, 2.126238]], {color: 'red'}).addTo(map);
L.polygon([[41.377865, 2.126413], [41.377979, 2.126296], [41.378055, 2.126427], [41.377828, 2.126661], [41.377751, 2.126530], [41.377865, 2.126413]], {color: 'red'}).addTo(map);
L.polygon([[41.377941, 2.126544], [41.378055, 2.126427], [41.378113, 2.126527], [41.377885, 2.126761], [41.377827, 2.126661], [41.377941, 2.126544]], {color: 'red'}).addTo(map);
L.polygon([[41.377999, 2.126644], [41.378113, 2.126527], [41.378192, 2.126664], [41.377964, 2.126897], [41.377885, 2.126761], [41.377999, 2.126644]], {color: 'red'}).addTo(map);
L.polygon([[41.378078, 2.126781], [41.378192, 2.126664], [41.378272, 2.126802], [41.378043, 2.127035], [41.377964, 2.126898], [41.378078, 2.126781]], {color: 'red'}).addTo(map);
L.polygon([[41.378157, 2.126918], [41.378273, 2.126804], [41.378361, 2.126964], [41.378130, 2.127191], [41.378041, 2.127032], [41.378157, 2.126918]], {color: 'red'}).addTo(map);
L.polygon([[41.378245, 2.127077], [41.378361, 2.126964], [41.378450, 2.127125], [41.378217, 2.127350], [41.378129, 2.127190], [41.378245, 2.127077]], {color: 'red'}).addTo(map);
L.polygon([[41.378333, 2.127238], [41.378450, 2.127126], [41.378544, 2.127298], [41.378310, 2.127522], [41.378216, 2.127350], [41.378333, 2.127238]], {color: 'red'}).addTo(map);
L.polygon([[41.378427, 2.127411], [41.378542, 2.127296], [41.378638, 2.127466], [41.378407, 2.127695], [41.378312, 2.127526], [41.378427, 2.127411]], {color: 'red'}).addTo(map);
L.polygon([[41.378523, 2.127582], [41.378637, 2.127464], [41.378713, 2.127595], [41.378486, 2.127830], [41.378409, 2.127700], [41.378523, 2.127582]], {color: 'red'}).addTo(map);
L.polygon([[41.378599, 2.127712], [41.378716, 2.127600], [41.378779, 2.127717], [41.378546, 2.127941], [41.378482, 2.127824], [41.378599, 2.127712]], {color: 'red'}).addTo(map);
L.polygon([[41.378685, 2.127871], [41.378802, 2.127759], [41.378877, 2.127897], [41.378644, 2.128122], [41.378568, 2.127983], [41.378685, 2.127871]], {color: 'red'}).addTo(map);
L.polygon([[41.378758, 2.128005], [41.378728, 2.127818], [41.378837, 2.127787], [41.378897, 2.128161], [41.378788, 2.128192], [41.378758, 2.128005]], {color: 'red'}).addTo(map);
L.polygon([[41.378928, 2.127956], [41.378887, 2.127877], [41.378993, 2.127781], [41.379074, 2.127938], [41.378969, 2.128035], [41.378928, 2.127956]], {color: 'red'}).addTo(map);
L.polygon([[41.379028, 2.127864], [41.378987, 2.127785], [41.379092, 2.127690], [41.379173, 2.127848], [41.379069, 2.127943], [41.379028, 2.127864]], {color: 'red'}).addTo(map);
L.polygon([[41.379132, 2.127769], [41.379092, 2.127690], [41.379192, 2.127600], [41.379272, 2.127759], [41.379172, 2.127848], [41.379132, 2.127769]], {color: 'red'}).addTo(map);
L.polygon([[41.379232, 2.127679], [41.379192, 2.127599], [41.379293, 2.127510], [41.379373, 2.127669], [41.379272, 2.127759], [41.379232, 2.127679]], {color: 'red'}).addTo(map);
L.polygon([[41.379333, 2.127589], [41.379289, 2.127514], [41.379388, 2.127410], [41.379477, 2.127561], [41.379377, 2.127664], [41.379333, 2.127589]], {color: 'red'}).addTo(map);
L.polygon([[41.379437, 2.127481], [41.379383, 2.127418], [41.379463, 2.127296], [41.379572, 2.127422], [41.379491, 2.127544], [41.379437, 2.127481]], {color: 'red'}).addTo(map);
L.polygon([[41.379517, 2.127359], [41.379463, 2.127296], [41.379544, 2.127174], [41.379652, 2.127301], [41.379571, 2.127422], [41.379517, 2.127359]], {color: 'red'}).addTo(map);
L.polygon([[41.379598, 2.127237], [41.379544, 2.127174], [41.379618, 2.127061], [41.379727, 2.127187], [41.379652, 2.127300], [41.379598, 2.127237]], {color: 'red'}).addTo(map);
L.polygon([[41.379672, 2.127124], [41.379618, 2.127061], [41.379682, 2.126964], [41.379790, 2.127090], [41.379726, 2.127187], [41.379672, 2.127124]], {color: 'red'}).addTo(map);
L.polygon([[41.379736, 2.127027], [41.379682, 2.126964], [41.379745, 2.126869], [41.379853, 2.126995], [41.379790, 2.127090], [41.379736, 2.127027]], {color: 'red'}).addTo(map);
L.polygon([[41.379799, 2.126932], [41.379745, 2.126869], [41.379830, 2.126740], [41.379939, 2.126866], [41.379853, 2.126995], [41.379799, 2.126932]], {color: 'red'}).addTo(map);
L.polygon([[41.379884, 2.126803], [41.379829, 2.126741], [41.379911, 2.126614], [41.380021, 2.126738], [41.379939, 2.126865], [41.379884, 2.126803]], {color: 'red'}).addTo(map);
L.polygon([[41.379967, 2.126674], [41.379911, 2.126614], [41.380003, 2.126460], [41.380116, 2.126580], [41.380023, 2.126734], [41.379967, 2.126674]], {color: 'red'}).addTo(map);
L.polygon([[41.380059, 2.126520], [41.380002, 2.126461], [41.380069, 2.126347], [41.380183, 2.126465], [41.380116, 2.126579], [41.380059, 2.126520]], {color: 'red'}).addTo(map);
L.polygon([[41.380126, 2.126406], [41.380070, 2.126346], [41.380128, 2.126249], [41.380241, 2.126369], [41.380182, 2.126466], [41.380126, 2.126406]], {color: 'red'}).addTo(map);
L.polygon([[41.380184, 2.126309], [41.380119, 2.126349], [41.380074, 2.126220], [41.380205, 2.126140], [41.380249, 2.126269], [41.380184, 2.126309]], {color: 'red'}).addTo(map);
L.polygon([[41.380144, 2.126193], [41.380117, 2.126224], [41.380046, 2.126113], [41.380101, 2.126051], [41.380171, 2.126162], [41.380144, 2.126193]], {color: 'red'}).addTo(map);
L.polygon([[41.380062, 2.126064], [41.380026, 2.126072], [41.380006, 2.125912], [41.380077, 2.125896], [41.380098, 2.126056], [41.380062, 2.126064]], {color: 'red'}).addTo(map);
L.polygon([[41.380042, 2.125905], [41.380011, 2.125930], [41.379950, 2.125797], [41.380011, 2.125747], [41.380073, 2.125880], [41.380042, 2.125905]], {color: 'red'}).addTo(map);
L.polygon([[41.379985, 2.125782], [41.379958, 2.125814], [41.379886, 2.125707], [41.379939, 2.125643], [41.380012, 2.125750], [41.379985, 2.125782]], {color: 'red'}).addTo(map);
L.polygon([[41.379913, 2.125675], [41.379886, 2.125706], [41.379821, 2.125608], [41.379875, 2.125545], [41.379940, 2.125644], [41.379913, 2.125675]], {color: 'red'}).addTo(map);
L.polygon([[41.379848, 2.125576], [41.379821, 2.125607], [41.379750, 2.125496], [41.379804, 2.125434], [41.379875, 2.125545], [41.379848, 2.125576]], {color: 'red'}).addTo(map);
L.polygon([[41.379777, 2.125465], [41.379750, 2.125496], [41.379680, 2.125389], [41.379735, 2.125327], [41.379804, 2.125434], [41.379777, 2.125465]], {color: 'red'}).addTo(map);
L.polygon([[41.379707, 2.125357], [41.379679, 2.125387], [41.379618, 2.125287], [41.379674, 2.125227], [41.379735, 2.125327], [41.379707, 2.125357]], {color: 'red'}).addTo(map);
L.polygon([[41.379646, 2.125256], [41.379618, 2.125285], [41.379561, 2.125189], [41.379618, 2.125131], [41.379674, 2.125227], [41.379646, 2.125256]], {color: 'red'}).addTo(map);
L.polygon([[41.379590, 2.125160], [41.379562, 2.125190], [41.379495, 2.125077], [41.379551, 2.125017], [41.379618, 2.125130], [41.379590, 2.125160]], {color: 'red'}).addTo(map);
L.polygon([[41.379523, 2.125047], [41.379495, 2.125077], [41.379437, 2.124979], [41.379494, 2.124920], [41.379551, 2.125017], [41.379523, 2.125047]], {color: 'red'}).addTo(map);
L.polygon([[41.379510, 2.125025], [41.379510, 2.124977], [41.379603, 2.124977], [41.379603, 2.125073], [41.379510, 2.125073], [41.379510, 2.125025]], {color: 'red'}).addTo(map);
L.polygon([[41.379510, 2.125025], [41.379510, 2.124977], [41.379603, 2.124977], [41.379603, 2.125073], [41.379510, 2.125073], [41.379510, 2.125025]], {color: 'red'}).addTo(map);
L.polygon([[41.379510, 2.125025], [41.379510, 2.124977], [41.379603, 2.124977], [41.379603, 2.125073], [41.379510, 2.125073], [41.379510, 2.125025]], {color: 'red'}).addTo(map);
L.polygon([[41.379510, 2.125025], [41.379510, 2.124977], [41.379603, 2.124977], [41.379603, 2.125073], [41.379510, 2.125073], [41.379510, 2.125025]], {color: 'red'}).addTo(map);
            L.marker([41.407019, 2.168555]).addTo(map).bindPopup(`<b>Point 0</b><br>Lat: 41.407019<br>Lon: 2.168555<br>Heading: 135.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.26 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.407019,2.168555&heading=135.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406992, 2.168591]).addTo(map).bindPopup(`<b>Point 1</b><br>Lat: 41.406992<br>Lon: 2.168591<br>Heading: 187.42<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.29 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406992,2.168591&heading=187.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406848, 2.168566]).addTo(map).bindPopup(`<b>Point 2</b><br>Lat: 41.406848<br>Lon: 2.168566<br>Heading: 224.85<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.52 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406848,2.168566&heading=224.85&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406750, 2.168436]).addTo(map).bindPopup(`<b>Point 3</b><br>Lat: 41.406750<br>Lon: 2.168436<br>Heading: 225.53<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.75 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406750,2.168436&heading=225.53&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406669, 2.168326]).addTo(map).bindPopup(`<b>Point 4</b><br>Lat: 41.406669<br>Lon: 2.168326<br>Heading: 225.69<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.02 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406669,2.168326&heading=225.69&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406587, 2.168214]).addTo(map).bindPopup(`<b>Point 5</b><br>Lat: 41.406587<br>Lon: 2.168214<br>Heading: 224.76<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.12 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406587,2.168214&heading=224.76&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406497, 2.168095]).addTo(map).bindPopup(`<b>Point 6</b><br>Lat: 41.406497<br>Lon: 2.168095<br>Heading: 225.33<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.45 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406497,2.168095&heading=225.33&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406431, 2.168006]).addTo(map).bindPopup(`<b>Point 7</b><br>Lat: 41.406431<br>Lon: 2.168006<br>Heading: 225.09<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.63 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406431,2.168006&heading=225.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406351, 2.167899]).addTo(map).bindPopup(`<b>Point 8</b><br>Lat: 41.406351<br>Lon: 2.167899<br>Heading: 224.85<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.12 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406351,2.167899&heading=224.85&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406253, 2.167769]).addTo(map).bindPopup(`<b>Point 9</b><br>Lat: 41.406253<br>Lon: 2.167769<br>Heading: 224.67<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.56 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406253,2.167769&heading=224.67&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406187, 2.167682]).addTo(map).bindPopup(`<b>Point 10</b><br>Lat: 41.406187<br>Lon: 2.167682<br>Heading: 225.18<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.48 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406187,2.167682&heading=225.18&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406108, 2.167576]).addTo(map).bindPopup(`<b>Point 11</b><br>Lat: 41.406108<br>Lon: 2.167576<br>Heading: 224.67<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.31 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406108,2.167576&heading=224.67&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.406042, 2.167489]).addTo(map).bindPopup(`<b>Point 12</b><br>Lat: 41.406042<br>Lon: 2.167489<br>Heading: 224.91<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.91 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.406042,2.167489&heading=224.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405966, 2.167388]).addTo(map).bindPopup(`<b>Point 13</b><br>Lat: 41.405966<br>Lon: 2.167388<br>Heading: 224.61<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.40 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405966,2.167388&heading=224.61&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405893, 2.167292]).addTo(map).bindPopup(`<b>Point 14</b><br>Lat: 41.405893<br>Lon: 2.167292<br>Heading: 224.84<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.01 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405893,2.167292&heading=224.84&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405804, 2.167174]).addTo(map).bindPopup(`<b>Point 15</b><br>Lat: 41.405804<br>Lon: 2.167174<br>Heading: 224.76<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.03 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405804,2.167174&heading=224.76&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405714, 2.167055]).addTo(map).bindPopup(`<b>Point 16</b><br>Lat: 41.405714<br>Lon: 2.167055<br>Heading: 224.78<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.09 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405714,2.167055&heading=224.78&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405618, 2.166928]).addTo(map).bindPopup(`<b>Point 17</b><br>Lat: 41.405618<br>Lon: 2.166928<br>Heading: 224.91<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.43 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405618,2.166928&heading=224.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405539, 2.166823]).addTo(map).bindPopup(`<b>Point 18</b><br>Lat: 41.405539<br>Lon: 2.166823<br>Heading: 224.49<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.03 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405539,2.166823&heading=224.49&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405455, 2.166713]).addTo(map).bindPopup(`<b>Point 19</b><br>Lat: 41.405455<br>Lon: 2.166713<br>Heading: 225.25<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.73 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405455,2.166713&heading=225.25&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405368, 2.166596]).addTo(map).bindPopup(`<b>Point 20</b><br>Lat: 41.405368<br>Lon: 2.166596<br>Heading: 224.95<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.14 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405368,2.166596&heading=224.95&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405220, 2.166399]).addTo(map).bindPopup(`<b>Point 21</b><br>Lat: 41.405220<br>Lon: 2.166399<br>Heading: 224.30<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.96 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405220,2.166399&heading=224.30&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405117, 2.166265]).addTo(map).bindPopup(`<b>Point 22</b><br>Lat: 41.405117<br>Lon: 2.166265<br>Heading: 224.65<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.85 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405117,2.166265&heading=224.65&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.405016, 2.166132]).addTo(map).bindPopup(`<b>Point 23</b><br>Lat: 41.405016<br>Lon: 2.166132<br>Heading: 224.70<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.71 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.405016,2.166132&heading=224.70&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.404922, 2.166008]).addTo(map).bindPopup(`<b>Point 24</b><br>Lat: 41.404922<br>Lon: 2.166008<br>Heading: 223.48<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.22 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.404922,2.166008&heading=223.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.404816, 2.165874]).addTo(map).bindPopup(`<b>Point 25</b><br>Lat: 41.404816<br>Lon: 2.165874<br>Heading: 223.93<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.82 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.404816,2.165874&heading=223.93&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.404714, 2.165743]).addTo(map).bindPopup(`<b>Point 26</b><br>Lat: 41.404714<br>Lon: 2.165743<br>Heading: 224.51<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.00 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.404714,2.165743&heading=224.51&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.404611, 2.165608]).addTo(map).bindPopup(`<b>Point 27</b><br>Lat: 41.404611<br>Lon: 2.165608<br>Heading: 224.92<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.84 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.404611,2.165608&heading=224.92&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.404523, 2.165491]).addTo(map).bindPopup(`<b>Point 28</b><br>Lat: 41.404523<br>Lon: 2.165491<br>Heading: 224.71<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.27 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.404523,2.165491&heading=224.71&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.404426, 2.165363]).addTo(map).bindPopup(`<b>Point 29</b><br>Lat: 41.404426<br>Lon: 2.165363<br>Heading: 225.72<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.72 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.404426,2.165363&heading=225.72&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.404328, 2.165229]).addTo(map).bindPopup(`<b>Point 30</b><br>Lat: 41.404328<br>Lon: 2.165229<br>Heading: 226.19<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.02 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.404328,2.165229&heading=226.19&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.404228, 2.165090]).addTo(map).bindPopup(`<b>Point 31</b><br>Lat: 41.404228<br>Lon: 2.165090<br>Heading: 226.60<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.18 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.404228,2.165090&heading=226.60&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.404128, 2.164949]).addTo(map).bindPopup(`<b>Point 32</b><br>Lat: 41.404128<br>Lon: 2.164949<br>Heading: 226.35<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.19 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.404128,2.164949&heading=226.35&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.404040, 2.164826]).addTo(map).bindPopup(`<b>Point 33</b><br>Lat: 41.404040<br>Lon: 2.164826<br>Heading: 226.21<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.31 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.404040,2.164826&heading=226.21&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.403976, 2.164737]).addTo(map).bindPopup(`<b>Point 34</b><br>Lat: 41.403976<br>Lon: 2.164737<br>Heading: 225.44<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.25 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.403976,2.164737&heading=225.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.403911, 2.164649]).addTo(map).bindPopup(`<b>Point 35</b><br>Lat: 41.403911<br>Lon: 2.164649<br>Heading: 224.74<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.63 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.403911,2.164649&heading=224.74&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.403830, 2.164542]).addTo(map).bindPopup(`<b>Point 36</b><br>Lat: 41.403830<br>Lon: 2.164542<br>Heading: 225.54<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.57 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.403830,2.164542&heading=225.54&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.403738, 2.164417]).addTo(map).bindPopup(`<b>Point 37</b><br>Lat: 41.403738<br>Lon: 2.164417<br>Heading: 225.09<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.14 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.403738,2.164417&heading=225.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.403655, 2.164306]).addTo(map).bindPopup(`<b>Point 38</b><br>Lat: 41.403655<br>Lon: 2.164306<br>Heading: 224.03<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.84 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.403655,2.164306&heading=224.03&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.403565, 2.164190]).addTo(map).bindPopup(`<b>Point 39</b><br>Lat: 41.403565<br>Lon: 2.164190<br>Heading: 225.21<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.89 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.403565,2.164190&heading=225.21&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.403495, 2.164096]).addTo(map).bindPopup(`<b>Point 40</b><br>Lat: 41.403495<br>Lon: 2.164096<br>Heading: 174.67<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.75 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.403495,2.164096&heading=174.67&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.403278, 2.164123]).addTo(map).bindPopup(`<b>Point 41</b><br>Lat: 41.403278<br>Lon: 2.164123<br>Heading: 136.31<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 14.10 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.403278,2.164123&heading=136.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.403183, 2.164244]).addTo(map).bindPopup(`<b>Point 42</b><br>Lat: 41.403183<br>Lon: 2.164244<br>Heading: 135.25<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.16 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.403183,2.164244&heading=135.25&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.403099, 2.164355]).addTo(map).bindPopup(`<b>Point 43</b><br>Lat: 41.403099<br>Lon: 2.164355<br>Heading: 135.43<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.96 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.403099,2.164355&heading=135.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.403016, 2.164464]).addTo(map).bindPopup(`<b>Point 44</b><br>Lat: 41.403016<br>Lon: 2.164464<br>Heading: 134.91<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.66 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.403016,2.164464&heading=134.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.402936, 2.164571]).addTo(map).bindPopup(`<b>Point 45</b><br>Lat: 41.402936<br>Lon: 2.164571<br>Heading: 135.17<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.04 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.402936,2.164571&heading=135.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.402853, 2.164681]).addTo(map).bindPopup(`<b>Point 46</b><br>Lat: 41.402853<br>Lon: 2.164681<br>Heading: 135.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.74 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.402853,2.164681&heading=135.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.402784, 2.164773]).addTo(map).bindPopup(`<b>Point 47</b><br>Lat: 41.402784<br>Lon: 2.164773<br>Heading: 135.24<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 14.40 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.402784,2.164773&heading=135.24&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.402694, 2.164892]).addTo(map).bindPopup(`<b>Point 48</b><br>Lat: 41.402694<br>Lon: 2.164892<br>Heading: 136.43<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.58 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.402694,2.164892&heading=136.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.402612, 2.164996]).addTo(map).bindPopup(`<b>Point 49</b><br>Lat: 41.402612<br>Lon: 2.164996<br>Heading: 136.48<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 16.56 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.402612,2.164996&heading=136.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.402503, 2.165134]).addTo(map).bindPopup(`<b>Point 50</b><br>Lat: 41.402503<br>Lon: 2.165134<br>Heading: 135.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 17.43 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.402503,2.165134&heading=135.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.402392, 2.165282]).addTo(map).bindPopup(`<b>Point 51</b><br>Lat: 41.402392<br>Lon: 2.165282<br>Heading: 135.25<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 17.99 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.402392,2.165282&heading=135.25&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.402277, 2.165434]).addTo(map).bindPopup(`<b>Point 52</b><br>Lat: 41.402277<br>Lon: 2.165434<br>Heading: 135.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 18.41 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.402277,2.165434&heading=135.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.402160, 2.165590]).addTo(map).bindPopup(`<b>Point 53</b><br>Lat: 41.402160<br>Lon: 2.165590<br>Heading: 134.73<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 16.91 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.402160,2.165590&heading=134.73&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.402053, 2.165734]).addTo(map).bindPopup(`<b>Point 54</b><br>Lat: 41.402053<br>Lon: 2.165734<br>Heading: 135.23<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 14.61 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.402053,2.165734&heading=135.23&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401960, 2.165857]).addTo(map).bindPopup(`<b>Point 55</b><br>Lat: 41.401960<br>Lon: 2.165857<br>Heading: 135.60<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.19 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401960,2.165857&heading=135.60&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401888, 2.165951]).addTo(map).bindPopup(`<b>Point 56</b><br>Lat: 41.401888<br>Lon: 2.165951<br>Heading: 135.30<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 14.68 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401888,2.165951&heading=135.30&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401794, 2.166075]).addTo(map).bindPopup(`<b>Point 57</b><br>Lat: 41.401794<br>Lon: 2.166075<br>Heading: 135.43<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 14.47 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401794,2.166075&heading=135.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401711, 2.166184]).addTo(map).bindPopup(`<b>Point 58</b><br>Lat: 41.401711<br>Lon: 2.166184<br>Heading: 222.93<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.33 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401711,2.166184&heading=222.93&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401636, 2.166091]).addTo(map).bindPopup(`<b>Point 59</b><br>Lat: 41.401636<br>Lon: 2.166091<br>Heading: 224.42<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.74 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401636,2.166091&heading=224.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401561, 2.165993]).addTo(map).bindPopup(`<b>Point 60</b><br>Lat: 41.401561<br>Lon: 2.165993<br>Heading: 224.89<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.01 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401561,2.165993&heading=224.89&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401497, 2.165908]).addTo(map).bindPopup(`<b>Point 61</b><br>Lat: 41.401497<br>Lon: 2.165908<br>Heading: 223.91<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.40 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401497,2.165908&heading=223.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401430, 2.165822]).addTo(map).bindPopup(`<b>Point 62</b><br>Lat: 41.401430<br>Lon: 2.165822<br>Heading: 224.28<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.90 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401430,2.165822&heading=224.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401360, 2.165731]).addTo(map).bindPopup(`<b>Point 63</b><br>Lat: 41.401360<br>Lon: 2.165731<br>Heading: 224.15<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.07 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401360,2.165731&heading=224.15&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401275, 2.165621]).addTo(map).bindPopup(`<b>Point 64</b><br>Lat: 41.401275<br>Lon: 2.165621<br>Heading: 224.31<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.88 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401275,2.165621&heading=224.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401192, 2.165513]).addTo(map).bindPopup(`<b>Point 65</b><br>Lat: 41.401192<br>Lon: 2.165513<br>Heading: 224.85<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.00 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401192,2.165513&heading=224.85&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.401097, 2.165387]).addTo(map).bindPopup(`<b>Point 66</b><br>Lat: 41.401097<br>Lon: 2.165387<br>Heading: 224.94<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 18.04 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.401097,2.165387&heading=224.94&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.400982, 2.165234]).addTo(map).bindPopup(`<b>Point 67</b><br>Lat: 41.400982<br>Lon: 2.165234<br>Heading: 222.82<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 18.05 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.400982,2.165234&heading=222.82&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.400863, 2.165087]).addTo(map).bindPopup(`<b>Point 68</b><br>Lat: 41.400863<br>Lon: 2.165087<br>Heading: 222.86<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.76 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.400863,2.165087&heading=222.86&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.400766, 2.164967]).addTo(map).bindPopup(`<b>Point 69</b><br>Lat: 41.400766<br>Lon: 2.164967<br>Heading: 223.09<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.55 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.400766,2.164967&heading=223.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.400677, 2.164856]).addTo(map).bindPopup(`<b>Point 70</b><br>Lat: 41.400677<br>Lon: 2.164856<br>Heading: 223.00<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.36 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.400677,2.164856&heading=223.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.400566, 2.164718]).addTo(map).bindPopup(`<b>Point 71</b><br>Lat: 41.400566<br>Lon: 2.164718<br>Heading: 224.52<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.41 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.400566,2.164718&heading=224.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.400492, 2.164621]).addTo(map).bindPopup(`<b>Point 72</b><br>Lat: 41.400492<br>Lon: 2.164621<br>Heading: 225.31<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.92 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.400492,2.164621&heading=225.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.400423, 2.164528]).addTo(map).bindPopup(`<b>Point 73</b><br>Lat: 41.400423<br>Lon: 2.164528<br>Heading: 224.28<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.92 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.400423,2.164528&heading=224.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.400353, 2.164437]).addTo(map).bindPopup(`<b>Point 74</b><br>Lat: 41.400353<br>Lon: 2.164437<br>Heading: 224.79<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.48 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.400353,2.164437&heading=224.79&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.400254, 2.164306]).addTo(map).bindPopup(`<b>Point 75</b><br>Lat: 41.400254<br>Lon: 2.164306<br>Heading: 225.18<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.97 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.400254,2.164306&heading=225.18&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.400172, 2.164196]).addTo(map).bindPopup(`<b>Point 76</b><br>Lat: 41.400172<br>Lon: 2.164196<br>Heading: 224.84<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.93 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.400172,2.164196&heading=224.84&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.400083, 2.164078]).addTo(map).bindPopup(`<b>Point 77</b><br>Lat: 41.400083<br>Lon: 2.164078<br>Heading: 226.23<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.67 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.400083,2.164078&heading=226.23&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399986, 2.163943]).addTo(map).bindPopup(`<b>Point 78</b><br>Lat: 41.399986<br>Lon: 2.163943<br>Heading: 225.63<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 19.59 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399986,2.163943&heading=225.63&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399862, 2.163774]).addTo(map).bindPopup(`<b>Point 79</b><br>Lat: 41.399862<br>Lon: 2.163774<br>Heading: 225.77<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.37 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399862,2.163774&heading=225.77&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399797, 2.163685]).addTo(map).bindPopup(`<b>Point 80</b><br>Lat: 41.399797<br>Lon: 2.163685<br>Heading: 226.06<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.73 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399797,2.163685&heading=226.06&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399724, 2.163584]).addTo(map).bindPopup(`<b>Point 81</b><br>Lat: 41.399724<br>Lon: 2.163584<br>Heading: 225.68<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.02 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399724,2.163584&heading=225.68&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399661, 2.163498]).addTo(map).bindPopup(`<b>Point 82</b><br>Lat: 41.399661<br>Lon: 2.163498<br>Heading: 225.72<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.60 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399661,2.163498&heading=225.72&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399582, 2.163390]).addTo(map).bindPopup(`<b>Point 83</b><br>Lat: 41.399582<br>Lon: 2.163390<br>Heading: 226.09<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.37 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399582,2.163390&heading=226.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399517, 2.163300]).addTo(map).bindPopup(`<b>Point 84</b><br>Lat: 41.399517<br>Lon: 2.163300<br>Heading: 225.00<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.99 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399517,2.163300&heading=225.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399442, 2.163200]).addTo(map).bindPopup(`<b>Point 85</b><br>Lat: 41.399442<br>Lon: 2.163200<br>Heading: 222.63<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.01 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399442,2.163200&heading=222.63&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399376, 2.163119]).addTo(map).bindPopup(`<b>Point 86</b><br>Lat: 41.399376<br>Lon: 2.163119<br>Heading: 222.12<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.94 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399376,2.163119&heading=222.12&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399303, 2.163031]).addTo(map).bindPopup(`<b>Point 87</b><br>Lat: 41.399303<br>Lon: 2.163031<br>Heading: 224.04<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 19.60 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399303,2.163031&heading=224.04&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399175, 2.162866]).addTo(map).bindPopup(`<b>Point 88</b><br>Lat: 41.399175<br>Lon: 2.162866<br>Heading: 225.42<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.67 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399175,2.162866&heading=225.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.399073, 2.162728]).addTo(map).bindPopup(`<b>Point 89</b><br>Lat: 41.399073<br>Lon: 2.162728<br>Heading: 225.85<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.76 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.399073,2.162728&heading=225.85&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398990, 2.162614]).addTo(map).bindPopup(`<b>Point 90</b><br>Lat: 41.398990<br>Lon: 2.162614<br>Heading: 225.77<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.38 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398990,2.162614&heading=225.77&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398925, 2.162525]).addTo(map).bindPopup(`<b>Point 91</b><br>Lat: 41.398925<br>Lon: 2.162525<br>Heading: 225.89<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.35 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398925,2.162525&heading=225.89&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398837, 2.162404]).addTo(map).bindPopup(`<b>Point 92</b><br>Lat: 41.398837<br>Lon: 2.162404<br>Heading: 225.28<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.08 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398837,2.162404&heading=225.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398759, 2.162299]).addTo(map).bindPopup(`<b>Point 93</b><br>Lat: 41.398759<br>Lon: 2.162299<br>Heading: 224.91<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.87 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398759,2.162299&heading=224.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398683, 2.162198]).addTo(map).bindPopup(`<b>Point 94</b><br>Lat: 41.398683<br>Lon: 2.162198<br>Heading: 225.36<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.74 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398683,2.162198&heading=225.36&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398603, 2.162090]).addTo(map).bindPopup(`<b>Point 95</b><br>Lat: 41.398603<br>Lon: 2.162090<br>Heading: 223.82<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.44 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398603,2.162090&heading=223.82&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398535, 2.162003]).addTo(map).bindPopup(`<b>Point 96</b><br>Lat: 41.398535<br>Lon: 2.162003<br>Heading: 223.09<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.42 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398535,2.162003&heading=223.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398454, 2.161902]).addTo(map).bindPopup(`<b>Point 97</b><br>Lat: 41.398454<br>Lon: 2.161902<br>Heading: 222.93<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.33 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398454,2.161902&heading=222.93&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398379, 2.161809]).addTo(map).bindPopup(`<b>Point 98</b><br>Lat: 41.398379<br>Lon: 2.161809<br>Heading: 224.22<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.36 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398379,2.161809&heading=224.22&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398305, 2.161713]).addTo(map).bindPopup(`<b>Point 99</b><br>Lat: 41.398305<br>Lon: 2.161713<br>Heading: 225.12<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.36 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398305,2.161713&heading=225.12&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398240, 2.161626]).addTo(map).bindPopup(`<b>Point 100</b><br>Lat: 41.398240<br>Lon: 2.161626<br>Heading: 225.07<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 18.21 m<br>turn_direction: through<br>Turn: left|left;through|through|through<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398240,2.161626&heading=225.07&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398124, 2.161471]).addTo(map).bindPopup(`<b>Point 101</b><br>Lat: 41.398124<br>Lon: 2.161471<br>Heading: 225.18<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.92 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398124,2.161471&heading=225.18&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.398042, 2.161361]).addTo(map).bindPopup(`<b>Point 102</b><br>Lat: 41.398042<br>Lon: 2.161361<br>Heading: 225.19<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.48 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.398042,2.161361&heading=225.19&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397963, 2.161255]).addTo(map).bindPopup(`<b>Point 103</b><br>Lat: 41.397963<br>Lon: 2.161255<br>Heading: 224.57<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.48 m<br>turn_direction: through<br>Turn: left|left;through|through|through<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397963,2.161255&heading=224.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397896, 2.161167]).addTo(map).bindPopup(`<b>Point 104</b><br>Lat: 41.397896<br>Lon: 2.161167<br>Heading: 224.25<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.52 m<br>turn_direction: through<br>Turn: left|left;through|through|through<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397896,2.161167&heading=224.25&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397809, 2.161054]).addTo(map).bindPopup(`<b>Point 105</b><br>Lat: 41.397809<br>Lon: 2.161054<br>Heading: 224.63<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.95 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397809,2.161054&heading=224.63&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397733, 2.160954]).addTo(map).bindPopup(`<b>Point 106</b><br>Lat: 41.397733<br>Lon: 2.160954<br>Heading: 224.06<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.98 m<br>turn_direction: through<br>Turn: left|left;through|through|through<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397733,2.160954&heading=224.06&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397664, 2.160865]).addTo(map).bindPopup(`<b>Point 107</b><br>Lat: 41.397664<br>Lon: 2.160865<br>Heading: 224.40<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.74 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397664,2.160865&heading=224.40&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397628, 2.160818]).addTo(map).bindPopup(`<b>Point 108</b><br>Lat: 41.397628<br>Lon: 2.160818<br>Heading: 162.66<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.56 m<br>turn_direction: left<br>Turn: left|left;through|through|through<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397628,2.160818&heading=162.66&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397419, 2.160905]).addTo(map).bindPopup(`<b>Point 109</b><br>Lat: 41.397419<br>Lon: 2.160905<br>Heading: 134.51<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.23 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397419,2.160905&heading=134.51&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397360, 2.160985]).addTo(map).bindPopup(`<b>Point 110</b><br>Lat: 41.397360<br>Lon: 2.160985<br>Heading: 135.50<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.08 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397360,2.160985&heading=135.50&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397289, 2.161078]).addTo(map).bindPopup(`<b>Point 111</b><br>Lat: 41.397289<br>Lon: 2.161078<br>Heading: 134.99<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.90 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397289,2.161078&heading=134.99&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397220, 2.161170]).addTo(map).bindPopup(`<b>Point 112</b><br>Lat: 41.397220<br>Lon: 2.161170<br>Heading: 136.08<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.67 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397220,2.161170&heading=136.08&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397153, 2.161256]).addTo(map).bindPopup(`<b>Point 113</b><br>Lat: 41.397153<br>Lon: 2.161256<br>Heading: 137.10<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.47 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397153,2.161256&heading=137.10&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.397086, 2.161339]).addTo(map).bindPopup(`<b>Point 114</b><br>Lat: 41.397086<br>Lon: 2.161339<br>Heading: 175.80<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.26 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.397086,2.161339&heading=175.80&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396994, 2.161348]).addTo(map).bindPopup(`<b>Point 115</b><br>Lat: 41.396994<br>Lon: 2.161348<br>Heading: 251.57<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.06 m<br>turn_direction: None<br>Turn: through|through|through<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396994,2.161348&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396960, 2.161212]).addTo(map).bindPopup(`<b>Point 116</b><br>Lat: 41.396960<br>Lon: 2.161212<br>Heading: 251.90<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.63 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396960,2.161212&heading=251.90&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396922, 2.161057]).addTo(map).bindPopup(`<b>Point 117</b><br>Lat: 41.396922<br>Lon: 2.161057<br>Heading: 251.47<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.87 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396922,2.161057&heading=251.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396880, 2.160890]).addTo(map).bindPopup(`<b>Point 118</b><br>Lat: 41.396880<br>Lon: 2.160890<br>Heading: 252.14<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.54 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396880,2.160890&heading=252.14&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396851, 2.160770]).addTo(map).bindPopup(`<b>Point 119</b><br>Lat: 41.396851<br>Lon: 2.160770<br>Heading: 251.31<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.90 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396851,2.160770&heading=251.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396817, 2.160636]).addTo(map).bindPopup(`<b>Point 120</b><br>Lat: 41.396817<br>Lon: 2.160636<br>Heading: 250.76<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.94 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396817,2.160636&heading=250.76&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396778, 2.160487]).addTo(map).bindPopup(`<b>Point 121</b><br>Lat: 41.396778<br>Lon: 2.160487<br>Heading: 250.17<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.77 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396778,2.160487&heading=250.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396745, 2.160365]).addTo(map).bindPopup(`<b>Point 122</b><br>Lat: 41.396745<br>Lon: 2.160365<br>Heading: 258.14<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.58 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396745,2.160365&heading=258.14&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396719, 2.160200]).addTo(map).bindPopup(`<b>Point 123</b><br>Lat: 41.396719<br>Lon: 2.160200<br>Heading: 260.03<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.12 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396719,2.160200&heading=260.03&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396695, 2.160018]).addTo(map).bindPopup(`<b>Point 124</b><br>Lat: 41.396695<br>Lon: 2.160018<br>Heading: 260.12<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.18 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396695,2.160018&heading=260.12&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396672, 2.159842]).addTo(map).bindPopup(`<b>Point 125</b><br>Lat: 41.396672<br>Lon: 2.159842<br>Heading: 262.92<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.59 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396672,2.159842&heading=262.92&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396653, 2.159638]).addTo(map).bindPopup(`<b>Point 126</b><br>Lat: 41.396653<br>Lon: 2.159638<br>Heading: 249.03<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.20 m<br>turn_direction: left<br>Turn: slight_left|slight_left|through|through<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396653,2.159638&heading=249.03&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396607, 2.159478]).addTo(map).bindPopup(`<b>Point 127</b><br>Lat: 41.396607<br>Lon: 2.159478<br>Heading: 249.52<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.51 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396607,2.159478&heading=249.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396556, 2.159296]).addTo(map).bindPopup(`<b>Point 128</b><br>Lat: 41.396556<br>Lon: 2.159296<br>Heading: 237.48<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.86 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396556,2.159296&heading=237.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396512, 2.159204]).addTo(map).bindPopup(`<b>Point 129</b><br>Lat: 41.396512<br>Lon: 2.159204<br>Heading: 229.15<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.64 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396512,2.159204&heading=229.15&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396416, 2.159056]).addTo(map).bindPopup(`<b>Point 130</b><br>Lat: 41.396416<br>Lon: 2.159056<br>Heading: 251.70<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.35 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396416,2.159056&heading=251.70&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396384, 2.158927]).addTo(map).bindPopup(`<b>Point 131</b><br>Lat: 41.396384<br>Lon: 2.158927<br>Heading: 251.49<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 20.37 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396384,2.158927&heading=251.49&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396326, 2.158696]).addTo(map).bindPopup(`<b>Point 132</b><br>Lat: 41.396326<br>Lon: 2.158696<br>Heading: 251.71<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.98 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396326,2.158696&heading=251.71&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396295, 2.158571]).addTo(map).bindPopup(`<b>Point 133</b><br>Lat: 41.396295<br>Lon: 2.158571<br>Heading: 251.99<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.79 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396295,2.158571&heading=251.99&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396265, 2.158448]).addTo(map).bindPopup(`<b>Point 134</b><br>Lat: 41.396265<br>Lon: 2.158448<br>Heading: 251.57<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.31 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396265,2.158448&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396233, 2.158320]).addTo(map).bindPopup(`<b>Point 135</b><br>Lat: 41.396233<br>Lon: 2.158320<br>Heading: 251.65<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 19.00 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396233,2.158320&heading=251.65&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396179, 2.158103]).addTo(map).bindPopup(`<b>Point 136</b><br>Lat: 41.396179<br>Lon: 2.158103<br>Heading: 251.57<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.90 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396179,2.158103&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396134, 2.157923]).addTo(map).bindPopup(`<b>Point 137</b><br>Lat: 41.396134<br>Lon: 2.157923<br>Heading: 251.47<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.68 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396134,2.157923&heading=251.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396089, 2.157744]).addTo(map).bindPopup(`<b>Point 138</b><br>Lat: 41.396089<br>Lon: 2.157744<br>Heading: 251.88<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396089,2.157744&heading=251.88&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396048, 2.157577]).addTo(map).bindPopup(`<b>Point 139</b><br>Lat: 41.396048<br>Lon: 2.157577<br>Heading: 251.67<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.21 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396048,2.157577&heading=251.67&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.396005, 2.157404]).addTo(map).bindPopup(`<b>Point 140</b><br>Lat: 41.396005<br>Lon: 2.157404<br>Heading: 251.42<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.15 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.396005,2.157404&heading=251.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395976, 2.157289]).addTo(map).bindPopup(`<b>Point 141</b><br>Lat: 41.395976<br>Lon: 2.157289<br>Heading: 251.46<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.31 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395976,2.157289&heading=251.46&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395935, 2.157126]).addTo(map).bindPopup(`<b>Point 142</b><br>Lat: 41.395935<br>Lon: 2.157126<br>Heading: 251.83<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.74 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395935,2.157126&heading=251.83&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395902, 2.156992]).addTo(map).bindPopup(`<b>Point 143</b><br>Lat: 41.395902<br>Lon: 2.156992<br>Heading: 251.46<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.02 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395902,2.156992&heading=251.46&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395862, 2.156833]).addTo(map).bindPopup(`<b>Point 144</b><br>Lat: 41.395862<br>Lon: 2.156833<br>Heading: 251.12<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.40 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395862,2.156833&heading=251.12&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395823, 2.156681]).addTo(map).bindPopup(`<b>Point 145</b><br>Lat: 41.395823<br>Lon: 2.156681<br>Heading: 250.86<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.93 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395823,2.156681&heading=250.86&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395779, 2.156512]).addTo(map).bindPopup(`<b>Point 146</b><br>Lat: 41.395779<br>Lon: 2.156512<br>Heading: 250.65<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.77 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395779,2.156512&heading=250.65&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395735, 2.156345]).addTo(map).bindPopup(`<b>Point 147</b><br>Lat: 41.395735<br>Lon: 2.156345<br>Heading: 251.15<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 18.30 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395735,2.156345&heading=251.15&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395682, 2.156138]).addTo(map).bindPopup(`<b>Point 148</b><br>Lat: 41.395682<br>Lon: 2.156138<br>Heading: 251.05<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.64 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395682,2.156138&heading=251.05&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395648, 2.156006]).addTo(map).bindPopup(`<b>Point 149</b><br>Lat: 41.395648<br>Lon: 2.156006<br>Heading: 251.16<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 18.38 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395648,2.156006&heading=251.16&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395594, 2.155795]).addTo(map).bindPopup(`<b>Point 150</b><br>Lat: 41.395594<br>Lon: 2.155795<br>Heading: 336.24<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.27 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395594,2.155795&heading=336.24&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395640, 2.155768]).addTo(map).bindPopup(`<b>Point 151</b><br>Lat: 41.395640<br>Lon: 2.155768<br>Heading: 338.00<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 19.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395640,2.155768&heading=338.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395653, 2.155761]).addTo(map).bindPopup(`<b>Point 152</b><br>Lat: 41.395653<br>Lon: 2.155761<br>Heading: 336.15<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.07 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395653,2.155761&heading=336.15&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395709, 2.155728]).addTo(map).bindPopup(`<b>Point 153</b><br>Lat: 41.395709<br>Lon: 2.155728<br>Heading: 238.98<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.31 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395709,2.155728&heading=238.98&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395414, 2.155074]).addTo(map).bindPopup(`<b>Point 154</b><br>Lat: 41.395414<br>Lon: 2.155074<br>Heading: 251.84<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.99 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395414,2.155074&heading=251.84&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395383, 2.154948]).addTo(map).bindPopup(`<b>Point 155</b><br>Lat: 41.395383<br>Lon: 2.154948<br>Heading: 251.06<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 20.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395383,2.154948&heading=251.06&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395322, 2.154711]).addTo(map).bindPopup(`<b>Point 156</b><br>Lat: 41.395322<br>Lon: 2.154711<br>Heading: 251.27<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.88 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395322,2.154711&heading=251.27&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395293, 2.154597]).addTo(map).bindPopup(`<b>Point 157</b><br>Lat: 41.395293<br>Lon: 2.154597<br>Heading: 251.86<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.34 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395293,2.154597&heading=251.86&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395264, 2.154479]).addTo(map).bindPopup(`<b>Point 158</b><br>Lat: 41.395264<br>Lon: 2.154479<br>Heading: 251.31<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.83 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395264,2.154479&heading=251.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395230, 2.154345]).addTo(map).bindPopup(`<b>Point 159</b><br>Lat: 41.395230<br>Lon: 2.154345<br>Heading: 251.43<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.17 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395230,2.154345&heading=251.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395198, 2.154218]).addTo(map).bindPopup(`<b>Point 160</b><br>Lat: 41.395198<br>Lon: 2.154218<br>Heading: 251.35<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.56 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395198,2.154218&heading=251.35&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395159, 2.154064]).addTo(map).bindPopup(`<b>Point 161</b><br>Lat: 41.395159<br>Lon: 2.154064<br>Heading: 251.13<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.30 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395159,2.154064&heading=251.13&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395129, 2.153947]).addTo(map).bindPopup(`<b>Point 162</b><br>Lat: 41.395129<br>Lon: 2.153947<br>Heading: 251.17<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.38 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395129,2.153947&heading=251.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395096, 2.153818]).addTo(map).bindPopup(`<b>Point 163</b><br>Lat: 41.395096<br>Lon: 2.153818<br>Heading: 251.13<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.77 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395096,2.153818&heading=251.13&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395056, 2.153662]).addTo(map).bindPopup(`<b>Point 164</b><br>Lat: 41.395056<br>Lon: 2.153662<br>Heading: 251.45<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.18 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395056,2.153662&heading=251.45&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.395021, 2.153523]).addTo(map).bindPopup(`<b>Point 165</b><br>Lat: 41.395021<br>Lon: 2.153523<br>Heading: 251.27<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.09 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.395021,2.153523&heading=251.27&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394992, 2.153409]).addTo(map).bindPopup(`<b>Point 166</b><br>Lat: 41.394992<br>Lon: 2.153409<br>Heading: 251.45<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.93 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394992,2.153409&heading=251.45&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394955, 2.153262]).addTo(map).bindPopup(`<b>Point 167</b><br>Lat: 41.394955<br>Lon: 2.153262<br>Heading: 250.95<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.26 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394955,2.153262&heading=250.95&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394919, 2.153123]).addTo(map).bindPopup(`<b>Point 168</b><br>Lat: 41.394919<br>Lon: 2.153123<br>Heading: 251.18<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.71 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394919,2.153123&heading=251.18&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394885, 2.152990]).addTo(map).bindPopup(`<b>Point 169</b><br>Lat: 41.394885<br>Lon: 2.152990<br>Heading: 251.57<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.62 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394885,2.152990&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394852, 2.152858]).addTo(map).bindPopup(`<b>Point 170</b><br>Lat: 41.394852<br>Lon: 2.152858<br>Heading: 250.78<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.47 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394852,2.152858&heading=250.78&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394818, 2.152728]).addTo(map).bindPopup(`<b>Point 171</b><br>Lat: 41.394818<br>Lon: 2.152728<br>Heading: 251.43<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 22.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394818,2.152728&heading=251.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394755, 2.152478]).addTo(map).bindPopup(`<b>Point 172</b><br>Lat: 41.394755<br>Lon: 2.152478<br>Heading: 251.02<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.35 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394755,2.152478&heading=251.02&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394707, 2.152292]).addTo(map).bindPopup(`<b>Point 173</b><br>Lat: 41.394707<br>Lon: 2.152292<br>Heading: 251.09<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.83 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394707,2.152292&heading=251.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394661, 2.152113]).addTo(map).bindPopup(`<b>Point 174</b><br>Lat: 41.394661<br>Lon: 2.152113<br>Heading: 251.69<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394661,2.152113&heading=251.69&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394627, 2.151976]).addTo(map).bindPopup(`<b>Point 175</b><br>Lat: 41.394627<br>Lon: 2.151976<br>Heading: 250.98<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.22 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394627,2.151976&heading=250.98&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394597, 2.151860]).addTo(map).bindPopup(`<b>Point 176</b><br>Lat: 41.394597<br>Lon: 2.151860<br>Heading: 251.32<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.18 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394597,2.151860&heading=251.32&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394562, 2.151722]).addTo(map).bindPopup(`<b>Point 177</b><br>Lat: 41.394562<br>Lon: 2.151722<br>Heading: 251.14<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.61 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394562,2.151722&heading=251.14&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394531, 2.151601]).addTo(map).bindPopup(`<b>Point 178</b><br>Lat: 41.394531<br>Lon: 2.151601<br>Heading: 251.00<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.58 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394531,2.151601&heading=251.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394500, 2.151481]).addTo(map).bindPopup(`<b>Point 179</b><br>Lat: 41.394500<br>Lon: 2.151481<br>Heading: 250.92<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.97 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394500,2.151481&heading=250.92&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394459, 2.151323]).addTo(map).bindPopup(`<b>Point 180</b><br>Lat: 41.394459<br>Lon: 2.151323<br>Heading: 251.83<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.74 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394459,2.151323&heading=251.83&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394426, 2.151189]).addTo(map).bindPopup(`<b>Point 181</b><br>Lat: 41.394426<br>Lon: 2.151189<br>Heading: 251.57<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.57 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394426,2.151189&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394393, 2.151057]).addTo(map).bindPopup(`<b>Point 182</b><br>Lat: 41.394393<br>Lon: 2.151057<br>Heading: 252.08<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.97 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394393,2.151057&heading=252.08&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394360, 2.150921]).addTo(map).bindPopup(`<b>Point 183</b><br>Lat: 41.394360<br>Lon: 2.150921<br>Heading: 251.44<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.54 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394360,2.150921&heading=251.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394327, 2.150790]).addTo(map).bindPopup(`<b>Point 184</b><br>Lat: 41.394327<br>Lon: 2.150790<br>Heading: 287.63<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.86 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394327,2.150790&heading=287.63&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394389, 2.150530]).addTo(map).bindPopup(`<b>Point 185</b><br>Lat: 41.394389<br>Lon: 2.150530<br>Heading: 251.57<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.24 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394389,2.150530&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394357, 2.150402]).addTo(map).bindPopup(`<b>Point 186</b><br>Lat: 41.394357<br>Lon: 2.150402<br>Heading: 251.17<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.40 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394357,2.150402&heading=251.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394324, 2.150273]).addTo(map).bindPopup(`<b>Point 187</b><br>Lat: 41.394324<br>Lon: 2.150273<br>Heading: 251.43<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.85 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394324,2.150273&heading=251.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394293, 2.150150]).addTo(map).bindPopup(`<b>Point 188</b><br>Lat: 41.394293<br>Lon: 2.150150<br>Heading: 251.31<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.47 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394293,2.150150&heading=251.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394260, 2.150020]).addTo(map).bindPopup(`<b>Point 189</b><br>Lat: 41.394260<br>Lon: 2.150020<br>Heading: 251.57<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.25 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394260,2.150020&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394228, 2.149892]).addTo(map).bindPopup(`<b>Point 190</b><br>Lat: 41.394228<br>Lon: 2.149892<br>Heading: 251.23<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.45 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394228,2.149892&heading=251.23&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394189, 2.149739]).addTo(map).bindPopup(`<b>Point 191</b><br>Lat: 41.394189<br>Lon: 2.149739<br>Heading: 251.57<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.97 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394189,2.149739&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394155, 2.149603]).addTo(map).bindPopup(`<b>Point 192</b><br>Lat: 41.394155<br>Lon: 2.149603<br>Heading: 252.22<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 14.08 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394155,2.149603&heading=252.22&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394117, 2.149445]).addTo(map).bindPopup(`<b>Point 193</b><br>Lat: 41.394117<br>Lon: 2.149445<br>Heading: 254.55<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.74 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394117,2.149445&heading=254.55&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394089, 2.149310]).addTo(map).bindPopup(`<b>Point 194</b><br>Lat: 41.394089<br>Lon: 2.149310<br>Heading: 251.68<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.54 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394089,2.149310&heading=251.68&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394050, 2.149153]).addTo(map).bindPopup(`<b>Point 195</b><br>Lat: 41.394050<br>Lon: 2.149153<br>Heading: 251.21<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.74 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394050,2.149153&heading=251.21&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.394013, 2.149008]).addTo(map).bindPopup(`<b>Point 196</b><br>Lat: 41.394013<br>Lon: 2.149008<br>Heading: 251.57<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.33 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.394013,2.149008&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393978, 2.148868]).addTo(map).bindPopup(`<b>Point 197</b><br>Lat: 41.393978<br>Lon: 2.148868<br>Heading: 251.57<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.26 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393978,2.148868&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393943, 2.148728]).addTo(map).bindPopup(`<b>Point 198</b><br>Lat: 41.393943<br>Lon: 2.148728<br>Heading: 251.28<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.35 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393943,2.148728&heading=251.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393913, 2.148610]).addTo(map).bindPopup(`<b>Point 199</b><br>Lat: 41.393913<br>Lon: 2.148610<br>Heading: 250.49<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.92 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393913,2.148610&heading=250.49&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393871, 2.148452]).addTo(map).bindPopup(`<b>Point 200</b><br>Lat: 41.393871<br>Lon: 2.148452<br>Heading: 250.52<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 18.70 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393871,2.148452&heading=250.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393815, 2.148241]).addTo(map).bindPopup(`<b>Point 201</b><br>Lat: 41.393815<br>Lon: 2.148241<br>Heading: 250.52<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.00 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393815,2.148241&heading=250.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393776, 2.148094]).addTo(map).bindPopup(`<b>Point 202</b><br>Lat: 41.393776<br>Lon: 2.148094<br>Heading: 250.56<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393776,2.148094&heading=250.56&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393740, 2.147958]).addTo(map).bindPopup(`<b>Point 203</b><br>Lat: 41.393740<br>Lon: 2.147958<br>Heading: 250.58<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.74 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393740,2.147958&heading=250.58&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393708, 2.147837]).addTo(map).bindPopup(`<b>Point 204</b><br>Lat: 41.393708<br>Lon: 2.147837<br>Heading: 251.13<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 17.22 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393708,2.147837&heading=251.13&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393658, 2.147642]).addTo(map).bindPopup(`<b>Point 205</b><br>Lat: 41.393658<br>Lon: 2.147642<br>Heading: 251.46<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.30 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393658,2.147642&heading=251.46&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393620, 2.147491]).addTo(map).bindPopup(`<b>Point 206</b><br>Lat: 41.393620<br>Lon: 2.147491<br>Heading: 252.14<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.61 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393620,2.147491&heading=252.14&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393591, 2.147371]).addTo(map).bindPopup(`<b>Point 207</b><br>Lat: 41.393591<br>Lon: 2.147371<br>Heading: 251.29<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.67 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393591,2.147371&heading=251.29&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393560, 2.147249]).addTo(map).bindPopup(`<b>Point 208</b><br>Lat: 41.393560<br>Lon: 2.147249<br>Heading: 251.57<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.73 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393560,2.147249&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393521, 2.147093]).addTo(map).bindPopup(`<b>Point 209</b><br>Lat: 41.393521<br>Lon: 2.147093<br>Heading: 251.81<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.86 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393521,2.147093&heading=251.81&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393485, 2.146947]).addTo(map).bindPopup(`<b>Point 210</b><br>Lat: 41.393485<br>Lon: 2.146947<br>Heading: 209.52<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.15 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393485,2.146947&heading=209.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393326, 2.146827]).addTo(map).bindPopup(`<b>Point 211</b><br>Lat: 41.393326<br>Lon: 2.146827<br>Heading: 251.57<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.23 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393326,2.146827&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393297, 2.146711]).addTo(map).bindPopup(`<b>Point 212</b><br>Lat: 41.393297<br>Lon: 2.146711<br>Heading: 251.29<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393297,2.146711&heading=251.29&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393266, 2.146589]).addTo(map).bindPopup(`<b>Point 213</b><br>Lat: 41.393266<br>Lon: 2.146589<br>Heading: 251.57<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.27 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393266,2.146589&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393234, 2.146461]).addTo(map).bindPopup(`<b>Point 214</b><br>Lat: 41.393234<br>Lon: 2.146461<br>Heading: 251.23<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.52 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393234,2.146461&heading=251.23&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393195, 2.146308]).addTo(map).bindPopup(`<b>Point 215</b><br>Lat: 41.393195<br>Lon: 2.146308<br>Heading: 251.71<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.02 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393195,2.146308&heading=251.71&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393164, 2.146183]).addTo(map).bindPopup(`<b>Point 216</b><br>Lat: 41.393164<br>Lon: 2.146183<br>Heading: 251.28<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.41 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393164,2.146183&heading=251.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393134, 2.146065]).addTo(map).bindPopup(`<b>Point 217</b><br>Lat: 41.393134<br>Lon: 2.146065<br>Heading: 251.07<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.34 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393134,2.146065&heading=251.07&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393099, 2.145929]).addTo(map).bindPopup(`<b>Point 218</b><br>Lat: 41.393099<br>Lon: 2.145929<br>Heading: 251.44<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.46 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393099,2.145929&heading=251.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393065, 2.145794]).addTo(map).bindPopup(`<b>Point 219</b><br>Lat: 41.393065<br>Lon: 2.145794<br>Heading: 251.90<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.34 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393065,2.145794&heading=251.90&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393027, 2.145639]).addTo(map).bindPopup(`<b>Point 220</b><br>Lat: 41.393027<br>Lon: 2.145639<br>Heading: 251.90<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.92 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393027,2.145639&heading=251.90&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392989, 2.145484]).addTo(map).bindPopup(`<b>Point 221</b><br>Lat: 41.392989<br>Lon: 2.145484<br>Heading: 251.79<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.54 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392989,2.145484&heading=251.79&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392951, 2.145330]).addTo(map).bindPopup(`<b>Point 222</b><br>Lat: 41.392951<br>Lon: 2.145330<br>Heading: 251.72<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.27 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392951,2.145330&heading=251.72&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392922, 2.145213]).addTo(map).bindPopup(`<b>Point 223</b><br>Lat: 41.392922<br>Lon: 2.145213<br>Heading: 250.10<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.70 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392922,2.145213&heading=250.10&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392881, 2.145062]).addTo(map).bindPopup(`<b>Point 224</b><br>Lat: 41.392881<br>Lon: 2.145062<br>Heading: 297.17<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392881,2.145062&heading=297.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392943, 2.144901]).addTo(map).bindPopup(`<b>Point 225</b><br>Lat: 41.392943<br>Lon: 2.144901<br>Heading: 317.10<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 10.64 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392943,2.144901&heading=317.10&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393010, 2.144818]).addTo(map).bindPopup(`<b>Point 226</b><br>Lat: 41.393010<br>Lon: 2.144818<br>Heading: 301.39<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 13.75 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393010,2.144818&heading=301.39&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393075, 2.144676]).addTo(map).bindPopup(`<b>Point 227</b><br>Lat: 41.393075<br>Lon: 2.144676<br>Heading: 284.11<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 10.05 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393075,2.144676&heading=284.11&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393098, 2.144554]).addTo(map).bindPopup(`<b>Point 228</b><br>Lat: 41.393098<br>Lon: 2.144554<br>Heading: 267.46<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 11.13 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393098,2.144554&heading=267.46&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393093, 2.144404]).addTo(map).bindPopup(`<b>Point 229</b><br>Lat: 41.393093<br>Lon: 2.144404<br>Heading: 246.98<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 14.96 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393093,2.144404&heading=246.98&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.393035, 2.144222]).addTo(map).bindPopup(`<b>Point 230</b><br>Lat: 41.393035<br>Lon: 2.144222<br>Heading: 222.39<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 16.11 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.393035,2.144222&heading=222.39&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392915, 2.144076]).addTo(map).bindPopup(`<b>Point 231</b><br>Lat: 41.392915<br>Lon: 2.144076<br>Heading: 200.89<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 11.76 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392915,2.144076&heading=200.89&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392799, 2.144017]).addTo(map).bindPopup(`<b>Point 232</b><br>Lat: 41.392799<br>Lon: 2.144017<br>Heading: 184.54<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 12.13 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392799,2.144017&heading=184.54&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392695, 2.144006]).addTo(map).bindPopup(`<b>Point 233</b><br>Lat: 41.392695<br>Lon: 2.144006<br>Heading: 220.33<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 12.00 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392695,2.144006&heading=220.33&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392619, 2.143920]).addTo(map).bindPopup(`<b>Point 234</b><br>Lat: 41.392619<br>Lon: 2.143920<br>Heading: 226.28<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.98 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392619,2.143920&heading=226.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392553, 2.143828]).addTo(map).bindPopup(`<b>Point 235</b><br>Lat: 41.392553<br>Lon: 2.143828<br>Heading: 236.08<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.38 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392553,2.143828&heading=236.08&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392498, 2.143719]).addTo(map).bindPopup(`<b>Point 236</b><br>Lat: 41.392498<br>Lon: 2.143719<br>Heading: 251.18<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.68 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392498,2.143719&heading=251.18&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392453, 2.143543]).addTo(map).bindPopup(`<b>Point 237</b><br>Lat: 41.392453<br>Lon: 2.143543<br>Heading: 251.17<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 18.96 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392453,2.143543&heading=251.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392398, 2.143328]).addTo(map).bindPopup(`<b>Point 238</b><br>Lat: 41.392398<br>Lon: 2.143328<br>Heading: 250.94<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 19.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392398,2.143328&heading=250.94&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392342, 2.143112]).addTo(map).bindPopup(`<b>Point 239</b><br>Lat: 41.392342<br>Lon: 2.143112<br>Heading: 251.05<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 20.56 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392342,2.143112&heading=251.05&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392282, 2.142879]).addTo(map).bindPopup(`<b>Point 240</b><br>Lat: 41.392282<br>Lon: 2.142879<br>Heading: 251.10<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 19.23 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392282,2.142879&heading=251.10&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392226, 2.142661]).addTo(map).bindPopup(`<b>Point 241</b><br>Lat: 41.392226<br>Lon: 2.142661<br>Heading: 250.99<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.95 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392226,2.142661&heading=250.99&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392188, 2.142514]).addTo(map).bindPopup(`<b>Point 242</b><br>Lat: 41.392188<br>Lon: 2.142514<br>Heading: 251.20<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 20.32 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392188,2.142514&heading=251.20&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392129, 2.142283]).addTo(map).bindPopup(`<b>Point 243</b><br>Lat: 41.392129<br>Lon: 2.142283<br>Heading: 251.13<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.35 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392129,2.142283&heading=251.13&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392099, 2.142166]).addTo(map).bindPopup(`<b>Point 244</b><br>Lat: 41.392099<br>Lon: 2.142166<br>Heading: 251.02<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.89 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392099,2.142166&heading=251.02&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392067, 2.142042]).addTo(map).bindPopup(`<b>Point 245</b><br>Lat: 41.392067<br>Lon: 2.142042<br>Heading: 251.31<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.48 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392067,2.142042&heading=251.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.392034, 2.141912]).addTo(map).bindPopup(`<b>Point 246</b><br>Lat: 41.392034<br>Lon: 2.141912<br>Heading: 251.00<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 21.18 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.392034,2.141912&heading=251.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391972, 2.141672]).addTo(map).bindPopup(`<b>Point 247</b><br>Lat: 41.391972<br>Lon: 2.141672<br>Heading: 251.42<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.11 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391972,2.141672&heading=251.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391943, 2.141557]).addTo(map).bindPopup(`<b>Point 248</b><br>Lat: 41.391943<br>Lon: 2.141557<br>Heading: 251.09<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.68 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391943,2.141557&heading=251.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391906, 2.141413]).addTo(map).bindPopup(`<b>Point 249</b><br>Lat: 41.391906<br>Lon: 2.141413<br>Heading: 250.98<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.23 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391906,2.141413&heading=250.98&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391876, 2.141297]).addTo(map).bindPopup(`<b>Point 250</b><br>Lat: 41.391876<br>Lon: 2.141297<br>Heading: 251.43<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.23 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391876,2.141297&heading=251.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391844, 2.141170]).addTo(map).bindPopup(`<b>Point 251</b><br>Lat: 41.391844<br>Lon: 2.141170<br>Heading: 251.44<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.85 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391844,2.141170&heading=251.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391810, 2.141035]).addTo(map).bindPopup(`<b>Point 252</b><br>Lat: 41.391810<br>Lon: 2.141035<br>Heading: 251.16<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391810,2.141035&heading=251.16&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391778, 2.140910]).addTo(map).bindPopup(`<b>Point 253</b><br>Lat: 41.391778<br>Lon: 2.140910<br>Heading: 250.98<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.20 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391778,2.140910&heading=250.98&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391748, 2.140794]).addTo(map).bindPopup(`<b>Point 254</b><br>Lat: 41.391748<br>Lon: 2.140794<br>Heading: 251.32<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 18.44 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391748,2.140794&heading=251.32&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391695, 2.140585]).addTo(map).bindPopup(`<b>Point 255</b><br>Lat: 41.391695<br>Lon: 2.140585<br>Heading: 251.42<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 19.51 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391695,2.140585&heading=251.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391639, 2.140363]).addTo(map).bindPopup(`<b>Point 256</b><br>Lat: 41.391639<br>Lon: 2.140363<br>Heading: 251.19<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 20.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391639,2.140363&heading=251.19&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391581, 2.140136]).addTo(map).bindPopup(`<b>Point 257</b><br>Lat: 41.391581<br>Lon: 2.140136<br>Heading: 251.41<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 18.51 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391581,2.140136&heading=251.41&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391528, 2.139926]).addTo(map).bindPopup(`<b>Point 258</b><br>Lat: 41.391528<br>Lon: 2.139926<br>Heading: 251.49<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 17.48 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391528,2.139926&heading=251.49&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391477, 2.139723]).addTo(map).bindPopup(`<b>Point 259</b><br>Lat: 41.391477<br>Lon: 2.139723<br>Heading: 251.33<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 19.46 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391477,2.139723&heading=251.33&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391422, 2.139506]).addTo(map).bindPopup(`<b>Point 260</b><br>Lat: 41.391422<br>Lon: 2.139506<br>Heading: 251.09<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 18.90 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391422,2.139506&heading=251.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391367, 2.139292]).addTo(map).bindPopup(`<b>Point 261</b><br>Lat: 41.391367<br>Lon: 2.139292<br>Heading: 250.39<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.31 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391367,2.139292&heading=250.39&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391336, 2.139176]).addTo(map).bindPopup(`<b>Point 262</b><br>Lat: 41.391336<br>Lon: 2.139176<br>Heading: 250.73<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.42 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391336,2.139176&heading=250.73&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391272, 2.138932]).addTo(map).bindPopup(`<b>Point 263</b><br>Lat: 41.391272<br>Lon: 2.138932<br>Heading: 250.87<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 19.24 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391272,2.138932&heading=250.87&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391215, 2.138713]).addTo(map).bindPopup(`<b>Point 264</b><br>Lat: 41.391215<br>Lon: 2.138713<br>Heading: 251.31<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 17.78 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391215,2.138713&heading=251.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391164, 2.138512]).addTo(map).bindPopup(`<b>Point 265</b><br>Lat: 41.391164<br>Lon: 2.138512<br>Heading: 251.57<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.52 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391164,2.138512&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391134, 2.138392]).addTo(map).bindPopup(`<b>Point 266</b><br>Lat: 41.391134<br>Lon: 2.138392<br>Heading: 251.30<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 16.61 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391134,2.138392&heading=251.30&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391086, 2.138203]).addTo(map).bindPopup(`<b>Point 267</b><br>Lat: 41.391086<br>Lon: 2.138203<br>Heading: 251.35<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 20.22 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391086,2.138203&heading=251.35&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.391028, 2.137974]).addTo(map).bindPopup(`<b>Point 268</b><br>Lat: 41.391028<br>Lon: 2.137974<br>Heading: 251.00<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.55 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.391028,2.137974&heading=251.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390997, 2.137854]).addTo(map).bindPopup(`<b>Point 269</b><br>Lat: 41.390997<br>Lon: 2.137854<br>Heading: 251.49<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 18.55 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390997,2.137854&heading=251.49&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390944, 2.137643]).addTo(map).bindPopup(`<b>Point 270</b><br>Lat: 41.390944<br>Lon: 2.137643<br>Heading: 251.43<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.46 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390944,2.137643&heading=251.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390914, 2.137524]).addTo(map).bindPopup(`<b>Point 271</b><br>Lat: 41.390914<br>Lon: 2.137524<br>Heading: 251.45<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.58 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390914,2.137524&heading=251.45&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390878, 2.137381]).addTo(map).bindPopup(`<b>Point 272</b><br>Lat: 41.390878<br>Lon: 2.137381<br>Heading: 251.70<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390878,2.137381&heading=251.70&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390845, 2.137248]).addTo(map).bindPopup(`<b>Point 273</b><br>Lat: 41.390845<br>Lon: 2.137248<br>Heading: 251.02<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.97 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390845,2.137248&heading=251.02&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390813, 2.137124]).addTo(map).bindPopup(`<b>Point 274</b><br>Lat: 41.390813<br>Lon: 2.137124<br>Heading: 251.32<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.15 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390813,2.137124&heading=251.32&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390778, 2.136986]).addTo(map).bindPopup(`<b>Point 275</b><br>Lat: 41.390778<br>Lon: 2.136986<br>Heading: 251.23<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.72 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390778,2.136986&heading=251.23&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390739, 2.136833]).addTo(map).bindPopup(`<b>Point 276</b><br>Lat: 41.390739<br>Lon: 2.136833<br>Heading: 251.42<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.91 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390739,2.136833&heading=251.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390710, 2.136718]).addTo(map).bindPopup(`<b>Point 277</b><br>Lat: 41.390710<br>Lon: 2.136718<br>Heading: 251.16<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 18.24 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390710,2.136718&heading=251.16&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390657, 2.136511]).addTo(map).bindPopup(`<b>Point 278</b><br>Lat: 41.390657<br>Lon: 2.136511<br>Heading: 251.49<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 19.62 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390657,2.136511&heading=251.49&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390601, 2.136288]).addTo(map).bindPopup(`<b>Point 279</b><br>Lat: 41.390601<br>Lon: 2.136288<br>Heading: 251.21<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 20.74 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390601,2.136288&heading=251.21&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390541, 2.136053]).addTo(map).bindPopup(`<b>Point 280</b><br>Lat: 41.390541<br>Lon: 2.136053<br>Heading: 250.95<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.26 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390541,2.136053&heading=250.95&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390505, 2.135914]).addTo(map).bindPopup(`<b>Point 281</b><br>Lat: 41.390505<br>Lon: 2.135914<br>Heading: 251.31<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.76 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390505,2.135914&heading=251.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390471, 2.135780]).addTo(map).bindPopup(`<b>Point 282</b><br>Lat: 41.390471<br>Lon: 2.135780<br>Heading: 251.70<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.05 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390471,2.135780&heading=251.70&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390437, 2.135643]).addTo(map).bindPopup(`<b>Point 283</b><br>Lat: 41.390437<br>Lon: 2.135643<br>Heading: 251.02<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.68 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390437,2.135643&heading=251.02&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390397, 2.135488]).addTo(map).bindPopup(`<b>Point 284</b><br>Lat: 41.390397<br>Lon: 2.135488<br>Heading: 251.34<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.88 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390397,2.135488&heading=251.34&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390360, 2.135342]).addTo(map).bindPopup(`<b>Point 285</b><br>Lat: 41.390360<br>Lon: 2.135342<br>Heading: 251.23<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.45 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390360,2.135342&heading=251.23&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390321, 2.135189]).addTo(map).bindPopup(`<b>Point 286</b><br>Lat: 41.390321<br>Lon: 2.135189<br>Heading: 251.28<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.41 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390321,2.135189&heading=251.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390291, 2.135071]).addTo(map).bindPopup(`<b>Point 287</b><br>Lat: 41.390291<br>Lon: 2.135071<br>Heading: 251.47<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.08 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390291,2.135071&heading=251.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390248, 2.134900]).addTo(map).bindPopup(`<b>Point 288</b><br>Lat: 41.390248<br>Lon: 2.134900<br>Heading: 251.34<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.88 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390248,2.134900&heading=251.34&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390211, 2.134754]).addTo(map).bindPopup(`<b>Point 289</b><br>Lat: 41.390211<br>Lon: 2.134754<br>Heading: 251.09<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.67 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390211,2.134754&heading=251.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390174, 2.134610]).addTo(map).bindPopup(`<b>Point 290</b><br>Lat: 41.390174<br>Lon: 2.134610<br>Heading: 251.34<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.81 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390174,2.134610&heading=251.34&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390137, 2.134464]).addTo(map).bindPopup(`<b>Point 291</b><br>Lat: 41.390137<br>Lon: 2.134464<br>Heading: 251.31<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.86 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390137,2.134464&heading=251.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390103, 2.134330]).addTo(map).bindPopup(`<b>Point 292</b><br>Lat: 41.390103<br>Lon: 2.134330<br>Heading: 251.35<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.53 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390103,2.134330&heading=251.35&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390064, 2.134176]).addTo(map).bindPopup(`<b>Point 293</b><br>Lat: 41.390064<br>Lon: 2.134176<br>Heading: 251.00<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.54 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390064,2.134176&heading=251.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.390033, 2.134056]).addTo(map).bindPopup(`<b>Point 294</b><br>Lat: 41.390033<br>Lon: 2.134056<br>Heading: 251.22<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.17 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.390033,2.134056&heading=251.22&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389995, 2.133907]).addTo(map).bindPopup(`<b>Point 295</b><br>Lat: 41.389995<br>Lon: 2.133907<br>Heading: 251.57<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.19 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389995,2.133907&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389966, 2.133791]).addTo(map).bindPopup(`<b>Point 296</b><br>Lat: 41.389966<br>Lon: 2.133791<br>Heading: 251.11<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.00 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389966,2.133791&heading=251.11&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389928, 2.133643]).addTo(map).bindPopup(`<b>Point 297</b><br>Lat: 41.389928<br>Lon: 2.133643<br>Heading: 251.15<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389928,2.133643&heading=251.15&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389897, 2.133522]).addTo(map).bindPopup(`<b>Point 298</b><br>Lat: 41.389897<br>Lon: 2.133522<br>Heading: 251.41<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 18.84 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389897,2.133522&heading=251.41&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389843, 2.133308]).addTo(map).bindPopup(`<b>Point 299</b><br>Lat: 41.389843<br>Lon: 2.133308<br>Heading: 251.17<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.41 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389843,2.133308&heading=251.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389810, 2.133179]).addTo(map).bindPopup(`<b>Point 300</b><br>Lat: 41.389810<br>Lon: 2.133179<br>Heading: 251.29<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.76 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389810,2.133179&heading=251.29&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389779, 2.133057]).addTo(map).bindPopup(`<b>Point 301</b><br>Lat: 41.389779<br>Lon: 2.133057<br>Heading: 251.43<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.44 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389779,2.133057&heading=251.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389749, 2.132938]).addTo(map).bindPopup(`<b>Point 302</b><br>Lat: 41.389749<br>Lon: 2.132938<br>Heading: 251.18<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.75 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389749,2.132938&heading=251.18&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389715, 2.132805]).addTo(map).bindPopup(`<b>Point 303</b><br>Lat: 41.389715<br>Lon: 2.132805<br>Heading: 251.43<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.80 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389715,2.132805&heading=251.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389684, 2.132682]).addTo(map).bindPopup(`<b>Point 304</b><br>Lat: 41.389684<br>Lon: 2.132682<br>Heading: 251.16<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.03 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389684,2.132682&heading=251.16&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389652, 2.132557]).addTo(map).bindPopup(`<b>Point 305</b><br>Lat: 41.389652<br>Lon: 2.132557<br>Heading: 250.95<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.30 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389652,2.132557&heading=250.95&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389616, 2.132418]).addTo(map).bindPopup(`<b>Point 306</b><br>Lat: 41.389616<br>Lon: 2.132418<br>Heading: 250.37<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 18.81 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389616,2.132418&heading=250.37&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389559, 2.132205]).addTo(map).bindPopup(`<b>Point 307</b><br>Lat: 41.389559<br>Lon: 2.132205<br>Heading: 251.32<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 18.41 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389559,2.132205&heading=251.32&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389506, 2.131996]).addTo(map).bindPopup(`<b>Point 308</b><br>Lat: 41.389506<br>Lon: 2.131996<br>Heading: 251.48<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 17.48 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389506,2.131996&heading=251.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389456, 2.131797]).addTo(map).bindPopup(`<b>Point 309</b><br>Lat: 41.389456<br>Lon: 2.131797<br>Heading: 251.34<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.92 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389456,2.131797&heading=251.34&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389419, 2.131651]).addTo(map).bindPopup(`<b>Point 310</b><br>Lat: 41.389419<br>Lon: 2.131651<br>Heading: 251.57<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.67 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389419,2.131651&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389383, 2.131507]).addTo(map).bindPopup(`<b>Point 311</b><br>Lat: 41.389383<br>Lon: 2.131507<br>Heading: 251.57<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.15 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389383,2.131507&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389354, 2.131391]).addTo(map).bindPopup(`<b>Point 312</b><br>Lat: 41.389354<br>Lon: 2.131391<br>Heading: 250.90<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.24 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389354,2.131391&heading=250.90&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389321, 2.131264]).addTo(map).bindPopup(`<b>Point 313</b><br>Lat: 41.389321<br>Lon: 2.131264<br>Heading: 251.57<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.74 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389321,2.131264&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389279, 2.131096]).addTo(map).bindPopup(`<b>Point 314</b><br>Lat: 41.389279<br>Lon: 2.131096<br>Heading: 251.79<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.91 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389279,2.131096&heading=251.79&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389240, 2.130938]).addTo(map).bindPopup(`<b>Point 315</b><br>Lat: 41.389240<br>Lon: 2.130938<br>Heading: 250.99<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.97 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389240,2.130938&heading=250.99&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389202, 2.130791]).addTo(map).bindPopup(`<b>Point 316</b><br>Lat: 41.389202<br>Lon: 2.130791<br>Heading: 252.00<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.40 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389202,2.130791&heading=252.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389173, 2.130672]).addTo(map).bindPopup(`<b>Point 317</b><br>Lat: 41.389173<br>Lon: 2.130672<br>Heading: 251.14<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.12 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389173,2.130672&heading=251.14&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389132, 2.130512]).addTo(map).bindPopup(`<b>Point 318</b><br>Lat: 41.389132<br>Lon: 2.130512<br>Heading: 251.69<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.36 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389132,2.130512&heading=251.69&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389097, 2.130371]).addTo(map).bindPopup(`<b>Point 319</b><br>Lat: 41.389097<br>Lon: 2.130371<br>Heading: 251.28<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.38 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389097,2.130371&heading=251.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389067, 2.130253]).addTo(map).bindPopup(`<b>Point 320</b><br>Lat: 41.389067<br>Lon: 2.130253<br>Heading: 251.57<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.63 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389067,2.130253&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389034, 2.130121]).addTo(map).bindPopup(`<b>Point 321</b><br>Lat: 41.389034<br>Lon: 2.130121<br>Heading: 251.44<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.59 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389034,2.130121&heading=251.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.389001, 2.129990]).addTo(map).bindPopup(`<b>Point 322</b><br>Lat: 41.389001<br>Lon: 2.129990<br>Heading: 251.44<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.12 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.389001,2.129990&heading=251.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388969, 2.129863]).addTo(map).bindPopup(`<b>Point 323</b><br>Lat: 41.388969<br>Lon: 2.129863<br>Heading: 251.27<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.30 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388969,2.129863&heading=251.27&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388925, 2.129690]).addTo(map).bindPopup(`<b>Point 324</b><br>Lat: 41.388925<br>Lon: 2.129690<br>Heading: 251.13<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.28 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388925,2.129690&heading=251.13&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388895, 2.129573]).addTo(map).bindPopup(`<b>Point 325</b><br>Lat: 41.388895<br>Lon: 2.129573<br>Heading: 251.57<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.97 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388895,2.129573&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388861, 2.129437]).addTo(map).bindPopup(`<b>Point 326</b><br>Lat: 41.388861<br>Lon: 2.129437<br>Heading: 251.34<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.87 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388861,2.129437&heading=251.34&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388824, 2.129291]).addTo(map).bindPopup(`<b>Point 327</b><br>Lat: 41.388824<br>Lon: 2.129291<br>Heading: 251.45<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.58 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388824,2.129291&heading=251.45&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388788, 2.129148]).addTo(map).bindPopup(`<b>Point 328</b><br>Lat: 41.388788<br>Lon: 2.129148<br>Heading: 251.42<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.11 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388788,2.129148&heading=251.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388759, 2.129033]).addTo(map).bindPopup(`<b>Point 329</b><br>Lat: 41.388759<br>Lon: 2.129033<br>Heading: 251.57<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.20 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388759,2.129033&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388727, 2.128905]).addTo(map).bindPopup(`<b>Point 330</b><br>Lat: 41.388727<br>Lon: 2.128905<br>Heading: 251.02<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.92 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388727,2.128905&heading=251.02&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388695, 2.128781]).addTo(map).bindPopup(`<b>Point 331</b><br>Lat: 41.388695<br>Lon: 2.128781<br>Heading: 251.84<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.10 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388695,2.128781&heading=251.84&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388664, 2.128655]).addTo(map).bindPopup(`<b>Point 332</b><br>Lat: 41.388664<br>Lon: 2.128655<br>Heading: 251.03<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.03 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388664,2.128655&heading=251.03&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388623, 2.128496]).addTo(map).bindPopup(`<b>Point 333</b><br>Lat: 41.388623<br>Lon: 2.128496<br>Heading: 251.67<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.23 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388623,2.128496&heading=251.67&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388580, 2.128323]).addTo(map).bindPopup(`<b>Point 334</b><br>Lat: 41.388580<br>Lon: 2.128323<br>Heading: 251.20<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.57 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388580,2.128323&heading=251.20&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388533, 2.128139]).addTo(map).bindPopup(`<b>Point 335</b><br>Lat: 41.388533<br>Lon: 2.128139<br>Heading: 251.47<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 16.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388533,2.128139&heading=251.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388489, 2.127964]).addTo(map).bindPopup(`<b>Point 336</b><br>Lat: 41.388489<br>Lon: 2.127964<br>Heading: 251.47<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.42 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388489,2.127964&heading=251.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388445, 2.127789]).addTo(map).bindPopup(`<b>Point 337</b><br>Lat: 41.388445<br>Lon: 2.127789<br>Heading: 251.47<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.36 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388445,2.127789&heading=251.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388404, 2.127626]).addTo(map).bindPopup(`<b>Point 338</b><br>Lat: 41.388404<br>Lon: 2.127626<br>Heading: 251.29<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.95 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388404,2.127626&heading=251.29&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388358, 2.127445]).addTo(map).bindPopup(`<b>Point 339</b><br>Lat: 41.388358<br>Lon: 2.127445<br>Heading: 251.67<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.51 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388358,2.127445&heading=251.67&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388317, 2.127280]).addTo(map).bindPopup(`<b>Point 340</b><br>Lat: 41.388317<br>Lon: 2.127280<br>Heading: 251.29<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.76 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388317,2.127280&heading=251.29&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388286, 2.127158]).addTo(map).bindPopup(`<b>Point 341</b><br>Lat: 41.388286<br>Lon: 2.127158<br>Heading: 251.46<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.23 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388286,2.127158&heading=251.46&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388248, 2.127007]).addTo(map).bindPopup(`<b>Point 342</b><br>Lat: 41.388248<br>Lon: 2.127007<br>Heading: 251.34<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.26 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388248,2.127007&heading=251.34&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388210, 2.126857]).addTo(map).bindPopup(`<b>Point 343</b><br>Lat: 41.388210<br>Lon: 2.126857<br>Heading: 251.46<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388210,2.126857&heading=251.46&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388170, 2.126698]).addTo(map).bindPopup(`<b>Point 344</b><br>Lat: 41.388170<br>Lon: 2.126698<br>Heading: 251.47<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.77 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388170,2.126698&heading=251.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388125, 2.126519]).addTo(map).bindPopup(`<b>Point 345</b><br>Lat: 41.388125<br>Lon: 2.126519<br>Heading: 251.79<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.87 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388125,2.126519&heading=251.79&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388086, 2.126361]).addTo(map).bindPopup(`<b>Point 346</b><br>Lat: 41.388086<br>Lon: 2.126361<br>Heading: 251.10<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 16.12 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388086,2.126361&heading=251.10&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388039, 2.126178]).addTo(map).bindPopup(`<b>Point 347</b><br>Lat: 41.388039<br>Lon: 2.126178<br>Heading: 251.43<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.85 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388039,2.126178&heading=251.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.388008, 2.126055]).addTo(map).bindPopup(`<b>Point 348</b><br>Lat: 41.388008<br>Lon: 2.126055<br>Heading: 251.87<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.02 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.388008,2.126055&heading=251.87&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387980, 2.125941]).addTo(map).bindPopup(`<b>Point 349</b><br>Lat: 41.387980<br>Lon: 2.125941<br>Heading: 251.72<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.20 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387980,2.125941&heading=251.72&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387951, 2.125824]).addTo(map).bindPopup(`<b>Point 350</b><br>Lat: 41.387951<br>Lon: 2.125824<br>Heading: 251.47<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.77 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387951,2.125824&heading=251.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387909, 2.125657]).addTo(map).bindPopup(`<b>Point 351</b><br>Lat: 41.387909<br>Lon: 2.125657<br>Heading: 251.30<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.10 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387909,2.125657&heading=251.30&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387877, 2.125531]).addTo(map).bindPopup(`<b>Point 352</b><br>Lat: 41.387877<br>Lon: 2.125531<br>Heading: 251.38<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 16.05 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387877,2.125531&heading=251.38&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387831, 2.125349]).addTo(map).bindPopup(`<b>Point 353</b><br>Lat: 41.387831<br>Lon: 2.125349<br>Heading: 251.57<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.12 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387831,2.125349&heading=251.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387802, 2.125233]).addTo(map).bindPopup(`<b>Point 354</b><br>Lat: 41.387802<br>Lon: 2.125233<br>Heading: 252.45<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.43 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387802,2.125233&heading=252.45&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387774, 2.125115]).addTo(map).bindPopup(`<b>Point 355</b><br>Lat: 41.387774<br>Lon: 2.125115<br>Heading: 251.47<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.32 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387774,2.125115&heading=251.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387733, 2.124952]).addTo(map).bindPopup(`<b>Point 356</b><br>Lat: 41.387733<br>Lon: 2.124952<br>Heading: 251.44<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.22 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387733,2.124952&heading=251.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387701, 2.124825]).addTo(map).bindPopup(`<b>Point 357</b><br>Lat: 41.387701<br>Lon: 2.124825<br>Heading: 252.13<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.88 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387701,2.124825&heading=252.13&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387671, 2.124701]).addTo(map).bindPopup(`<b>Point 358</b><br>Lat: 41.387671<br>Lon: 2.124701<br>Heading: 251.45<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.17 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387671,2.124701&heading=251.45&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387636, 2.124562]).addTo(map).bindPopup(`<b>Point 359</b><br>Lat: 41.387636<br>Lon: 2.124562<br>Heading: 251.77<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.99 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387636,2.124562&heading=251.77&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387594, 2.124392]).addTo(map).bindPopup(`<b>Point 360</b><br>Lat: 41.387594<br>Lon: 2.124392<br>Heading: 251.69<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387594,2.124392&heading=251.69&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387558, 2.124247]).addTo(map).bindPopup(`<b>Point 361</b><br>Lat: 41.387558<br>Lon: 2.124247<br>Heading: 251.80<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.25 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387558,2.124247&heading=251.80&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387521, 2.124097]).addTo(map).bindPopup(`<b>Point 362</b><br>Lat: 41.387521<br>Lon: 2.124097<br>Heading: 251.47<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.33 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387521,2.124097&heading=251.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387477, 2.123922]).addTo(map).bindPopup(`<b>Point 363</b><br>Lat: 41.387477<br>Lon: 2.123922<br>Heading: 251.72<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.03 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387477,2.123922&heading=251.72&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387449, 2.123809]).addTo(map).bindPopup(`<b>Point 364</b><br>Lat: 41.387449<br>Lon: 2.123809<br>Heading: 251.87<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.02 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387449,2.123809&heading=251.87&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387421, 2.123695]).addTo(map).bindPopup(`<b>Point 365</b><br>Lat: 41.387421<br>Lon: 2.123695<br>Heading: 251.29<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.73 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387421,2.123695&heading=251.29&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387390, 2.123573]).addTo(map).bindPopup(`<b>Point 366</b><br>Lat: 41.387390<br>Lon: 2.123573<br>Heading: 250.97<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.28 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387390,2.123573&heading=250.97&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387346, 2.123403]).addTo(map).bindPopup(`<b>Point 367</b><br>Lat: 41.387346<br>Lon: 2.123403<br>Heading: 302.86<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.11 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387346,2.123403&heading=302.86&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387441, 2.123207]).addTo(map).bindPopup(`<b>Point 368</b><br>Lat: 41.387441<br>Lon: 2.123207<br>Heading: 327.59<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.43 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387441,2.123207&heading=327.59&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387532, 2.123130]).addTo(map).bindPopup(`<b>Point 369</b><br>Lat: 41.387532<br>Lon: 2.123130<br>Heading: 324.07<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.58 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387532,2.123130&heading=324.07&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387620, 2.123045]).addTo(map).bindPopup(`<b>Point 370</b><br>Lat: 41.387620<br>Lon: 2.123045<br>Heading: 292.15<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387620,2.123045&heading=292.15&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387660, 2.122914]).addTo(map).bindPopup(`<b>Point 371</b><br>Lat: 41.387660<br>Lon: 2.122914<br>Heading: 291.60<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.44 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387660,2.122914&heading=291.60&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387701, 2.122776]).addTo(map).bindPopup(`<b>Point 372</b><br>Lat: 41.387701<br>Lon: 2.122776<br>Heading: 278.95<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.89 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387701,2.122776&heading=278.95&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387716, 2.122649]).addTo(map).bindPopup(`<b>Point 373</b><br>Lat: 41.387716<br>Lon: 2.122649<br>Heading: 268.52<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.60 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387716,2.122649&heading=268.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387713, 2.122494]).addTo(map).bindPopup(`<b>Point 374</b><br>Lat: 41.387713<br>Lon: 2.122494<br>Heading: 252.17<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 10.77 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387713,2.122494&heading=252.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387678, 2.122349]).addTo(map).bindPopup(`<b>Point 375</b><br>Lat: 41.387678<br>Lon: 2.122349<br>Heading: 229.56<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 11.43 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387678,2.122349&heading=229.56&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387600, 2.122227]).addTo(map).bindPopup(`<b>Point 376</b><br>Lat: 41.387600<br>Lon: 2.122227<br>Heading: 213.19<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 10.37 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387600,2.122227&heading=213.19&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387522, 2.122159]).addTo(map).bindPopup(`<b>Point 377</b><br>Lat: 41.387522<br>Lon: 2.122159<br>Heading: 204.95<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 11.81 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387522,2.122159&heading=204.95&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387422, 2.122097]).addTo(map).bindPopup(`<b>Point 378</b><br>Lat: 41.387422<br>Lon: 2.122097<br>Heading: 196.02<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 12.89 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387422,2.122097&heading=196.02&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387307, 2.122053]).addTo(map).bindPopup(`<b>Point 379</b><br>Lat: 41.387307<br>Lon: 2.122053<br>Heading: 189.76<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 10.82 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387307,2.122053&heading=189.76&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387198, 2.122028]).addTo(map).bindPopup(`<b>Point 380</b><br>Lat: 41.387198<br>Lon: 2.122028<br>Heading: 171.64<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 10.20 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387198,2.122028&heading=171.64&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.387101, 2.122047]).addTo(map).bindPopup(`<b>Point 381</b><br>Lat: 41.387101<br>Lon: 2.122047<br>Heading: 158.19<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 14.43 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.387101,2.122047&heading=158.19&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.386951, 2.122127]).addTo(map).bindPopup(`<b>Point 382</b><br>Lat: 41.386951<br>Lon: 2.122127<br>Heading: 141.77<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.90 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.386951,2.122127&heading=141.77&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.386871, 2.122211]).addTo(map).bindPopup(`<b>Point 383</b><br>Lat: 41.386871<br>Lon: 2.122211<br>Heading: 130.22<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.26 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.386871,2.122211&heading=130.22&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.386779, 2.122356]).addTo(map).bindPopup(`<b>Point 384</b><br>Lat: 41.386779<br>Lon: 2.122356<br>Heading: 129.94<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 18.33 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.386779,2.122356&heading=129.94&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.386681, 2.122512]).addTo(map).bindPopup(`<b>Point 385</b><br>Lat: 41.386681<br>Lon: 2.122512<br>Heading: 161.43<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 17.03 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.386681,2.122512&heading=161.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.386547, 2.122572]).addTo(map).bindPopup(`<b>Point 386</b><br>Lat: 41.386547<br>Lon: 2.122572<br>Heading: 179.34<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 15.55 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.386547,2.122572&heading=179.34&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.386417, 2.122574]).addTo(map).bindPopup(`<b>Point 387</b><br>Lat: 41.386417<br>Lon: 2.122574<br>Heading: 186.56<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.92 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.386417,2.122574&heading=186.56&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.386293, 2.122555]).addTo(map).bindPopup(`<b>Point 388</b><br>Lat: 41.386293<br>Lon: 2.122555<br>Heading: 186.40<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.17 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.386293,2.122555&heading=186.40&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.386166, 2.122536]).addTo(map).bindPopup(`<b>Point 389</b><br>Lat: 41.386166<br>Lon: 2.122536<br>Heading: 186.72<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.60 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.386166,2.122536&heading=186.72&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.386045, 2.122517]).addTo(map).bindPopup(`<b>Point 390</b><br>Lat: 41.386045<br>Lon: 2.122517<br>Heading: 184.67<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.386045,2.122517&heading=184.67&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.385944, 2.122506]).addTo(map).bindPopup(`<b>Point 391</b><br>Lat: 41.385944<br>Lon: 2.122506<br>Heading: 183.64<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.71 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.385944,2.122506&heading=183.64&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.385838, 2.122497]).addTo(map).bindPopup(`<b>Point 392</b><br>Lat: 41.385838<br>Lon: 2.122497<br>Heading: 182.98<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.28 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.385838,2.122497&heading=182.98&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.385737, 2.122490]).addTo(map).bindPopup(`<b>Point 393</b><br>Lat: 41.385737<br>Lon: 2.122490<br>Heading: 183.12<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.28 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.385737,2.122490&heading=183.12&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.385627, 2.122482]).addTo(map).bindPopup(`<b>Point 394</b><br>Lat: 41.385627<br>Lon: 2.122482<br>Heading: 183.52<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.67 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.385627,2.122482&heading=183.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.385505, 2.122472]).addTo(map).bindPopup(`<b>Point 395</b><br>Lat: 41.385505<br>Lon: 2.122472<br>Heading: 184.77<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.03 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.385505,2.122472&heading=184.77&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.385406, 2.122461]).addTo(map).bindPopup(`<b>Point 396</b><br>Lat: 41.385406<br>Lon: 2.122461<br>Heading: 184.68<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.28 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.385406,2.122461&heading=184.68&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.385296, 2.122449]).addTo(map).bindPopup(`<b>Point 397</b><br>Lat: 41.385296<br>Lon: 2.122449<br>Heading: 184.52<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.75 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.385296,2.122449&heading=184.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.385182, 2.122437]).addTo(map).bindPopup(`<b>Point 398</b><br>Lat: 41.385182<br>Lon: 2.122437<br>Heading: 184.57<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.94 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.385182,2.122437&heading=184.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.385060, 2.122424]).addTo(map).bindPopup(`<b>Point 399</b><br>Lat: 41.385060<br>Lon: 2.122424<br>Heading: 184.56<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.62 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.385060,2.122424&heading=184.56&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.384966, 2.122414]).addTo(map).bindPopup(`<b>Point 400</b><br>Lat: 41.384966<br>Lon: 2.122414<br>Heading: 187.64<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.79 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.384966,2.122414&heading=187.64&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.384843, 2.122392]).addTo(map).bindPopup(`<b>Point 401</b><br>Lat: 41.384843<br>Lon: 2.122392<br>Heading: 191.85<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.10 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.384843,2.122392&heading=191.85&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.384750, 2.122366]).addTo(map).bindPopup(`<b>Point 402</b><br>Lat: 41.384750<br>Lon: 2.122366<br>Heading: 201.12<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 15.05 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.384750,2.122366&heading=201.12&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.384616, 2.122297]).addTo(map).bindPopup(`<b>Point 403</b><br>Lat: 41.384616<br>Lon: 2.122297<br>Heading: 175.81<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.81 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.384616,2.122297&heading=175.81&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.384483, 2.122310]).addTo(map).bindPopup(`<b>Point 404</b><br>Lat: 41.384483<br>Lon: 2.122310<br>Heading: 172.32<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.384483,2.122310&heading=172.32&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.384355, 2.122333]).addTo(map).bindPopup(`<b>Point 405</b><br>Lat: 41.384355<br>Lon: 2.122333<br>Heading: 188.41<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 15.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.384355,2.122333&heading=188.41&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.384223, 2.122307]).addTo(map).bindPopup(`<b>Point 406</b><br>Lat: 41.384223<br>Lon: 2.122307<br>Heading: 178.48<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 15.60 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.384223,2.122307&heading=178.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.384082, 2.122312]).addTo(map).bindPopup(`<b>Point 407</b><br>Lat: 41.384082<br>Lon: 2.122312<br>Heading: 177.79<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 15.14 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.384082,2.122312&heading=177.79&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.383946, 2.122319]).addTo(map).bindPopup(`<b>Point 408</b><br>Lat: 41.383946<br>Lon: 2.122319<br>Heading: 177.72<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 14.66 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.383946,2.122319&heading=177.72&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.383814, 2.122326]).addTo(map).bindPopup(`<b>Point 409</b><br>Lat: 41.383814<br>Lon: 2.122326<br>Heading: 178.48<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.71 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.383814,2.122326&heading=178.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.383701, 2.122330]).addTo(map).bindPopup(`<b>Point 410</b><br>Lat: 41.383701<br>Lon: 2.122330<br>Heading: 178.13<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.31 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.383701,2.122330&heading=178.13&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.383609, 2.122334]).addTo(map).bindPopup(`<b>Point 411</b><br>Lat: 41.383609<br>Lon: 2.122334<br>Heading: 178.72<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.68 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.383609,2.122334&heading=178.72&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.383508, 2.122337]).addTo(map).bindPopup(`<b>Point 412</b><br>Lat: 41.383508<br>Lon: 2.122337<br>Heading: 178.44<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.24 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.383508,2.122337&heading=178.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.383398, 2.122341]).addTo(map).bindPopup(`<b>Point 413</b><br>Lat: 41.383398<br>Lon: 2.122341<br>Heading: 178.61<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.44 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.383398,2.122341&heading=178.61&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.383305, 2.122344]).addTo(map).bindPopup(`<b>Point 414</b><br>Lat: 41.383305<br>Lon: 2.122344<br>Heading: 178.45<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.34 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.383305,2.122344&heading=178.45&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.383194, 2.122348]).addTo(map).bindPopup(`<b>Point 415</b><br>Lat: 41.383194<br>Lon: 2.122348<br>Heading: 178.28<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.58 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.383194,2.122348&heading=178.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.383094, 2.122352]).addTo(map).bindPopup(`<b>Point 416</b><br>Lat: 41.383094<br>Lon: 2.122352<br>Heading: 178.33<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.09 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.383094,2.122352&heading=178.33&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.382991, 2.122356]).addTo(map).bindPopup(`<b>Point 417</b><br>Lat: 41.382991<br>Lon: 2.122356<br>Heading: 179.31<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 14.08 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.382991,2.122356&heading=179.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.382867, 2.122358]).addTo(map).bindPopup(`<b>Point 418</b><br>Lat: 41.382867<br>Lon: 2.122358<br>Heading: 193.74<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 20.86 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.382867,2.122358&heading=193.74&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.382600, 2.122271]).addTo(map).bindPopup(`<b>Point 419</b><br>Lat: 41.382600<br>Lon: 2.122271<br>Heading: 221.20<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.78 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.382600,2.122271&heading=221.20&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.382522, 2.122180]).addTo(map).bindPopup(`<b>Point 420</b><br>Lat: 41.382522<br>Lon: 2.122180<br>Heading: 218.71<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 18.88 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.382522,2.122180&heading=218.71&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.382390, 2.122039]).addTo(map).bindPopup(`<b>Point 421</b><br>Lat: 41.382390<br>Lon: 2.122039<br>Heading: 218.01<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.17 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.382390,2.122039&heading=218.01&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.382318, 2.121964]).addTo(map).bindPopup(`<b>Point 422</b><br>Lat: 41.382318<br>Lon: 2.121964<br>Heading: 217.91<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.16 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.382318,2.121964&heading=217.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.382239, 2.121882]).addTo(map).bindPopup(`<b>Point 423</b><br>Lat: 41.382239<br>Lon: 2.121882<br>Heading: 218.25<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.72 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.382239,2.121882&heading=218.25&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.382121, 2.121758]).addTo(map).bindPopup(`<b>Point 424</b><br>Lat: 41.382121<br>Lon: 2.121758<br>Heading: 217.91<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.95 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.382121,2.121758&heading=217.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.382015, 2.121648]).addTo(map).bindPopup(`<b>Point 425</b><br>Lat: 41.382015<br>Lon: 2.121648<br>Heading: 218.06<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.382015,2.121648&heading=218.06&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381923, 2.121552]).addTo(map).bindPopup(`<b>Point 426</b><br>Lat: 41.381923<br>Lon: 2.121552<br>Heading: 218.14<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.16 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381923,2.121552&heading=218.14&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381837, 2.121462]).addTo(map).bindPopup(`<b>Point 427</b><br>Lat: 41.381837<br>Lon: 2.121462<br>Heading: 217.82<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.20 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381837,2.121462&heading=217.82&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381750, 2.121372]).addTo(map).bindPopup(`<b>Point 428</b><br>Lat: 41.381750<br>Lon: 2.121372<br>Heading: 218.10<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.62 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381750,2.121372&heading=218.10&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381661, 2.121279]).addTo(map).bindPopup(`<b>Point 429</b><br>Lat: 41.381661<br>Lon: 2.121279<br>Heading: 218.06<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.02 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381661,2.121279&heading=218.06&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381569, 2.121183]).addTo(map).bindPopup(`<b>Point 430</b><br>Lat: 41.381569<br>Lon: 2.121183<br>Heading: 218.20<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.54 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381569,2.121183&heading=218.20&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381487, 2.121097]).addTo(map).bindPopup(`<b>Point 431</b><br>Lat: 41.381487<br>Lon: 2.121097<br>Heading: 218.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.33 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381487,2.121097&heading=218.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381414, 2.121021]).addTo(map).bindPopup(`<b>Point 432</b><br>Lat: 41.381414<br>Lon: 2.121021<br>Heading: 244.78<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.08 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381414,2.121021&heading=244.78&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381373, 2.120905]).addTo(map).bindPopup(`<b>Point 433</b><br>Lat: 41.381373<br>Lon: 2.120905<br>Heading: 245.85<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.07 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381373,2.120905&heading=245.85&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381336, 2.120795]).addTo(map).bindPopup(`<b>Point 434</b><br>Lat: 41.381336<br>Lon: 2.120795<br>Heading: 258.96<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.10 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381336,2.120795&heading=258.96&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381318, 2.120672]).addTo(map).bindPopup(`<b>Point 435</b><br>Lat: 41.381318<br>Lon: 2.120672<br>Heading: 263.71<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.18 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381318,2.120672&heading=263.71&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381306, 2.120527]).addTo(map).bindPopup(`<b>Point 436</b><br>Lat: 41.381306<br>Lon: 2.120527<br>Heading: 271.16<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.29 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381306,2.120527&heading=271.16&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381308, 2.120395]).addTo(map).bindPopup(`<b>Point 437</b><br>Lat: 41.381308<br>Lon: 2.120395<br>Heading: 272.74<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.60 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381308,2.120395&heading=272.74&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381313, 2.120256]).addTo(map).bindPopup(`<b>Point 438</b><br>Lat: 41.381313<br>Lon: 2.120256<br>Heading: 269.04<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.10 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381313,2.120256&heading=269.04&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381311, 2.120097]).addTo(map).bindPopup(`<b>Point 439</b><br>Lat: 41.381311<br>Lon: 2.120097<br>Heading: 268.68<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.25 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381311,2.120097&heading=268.68&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381309, 2.119981]).addTo(map).bindPopup(`<b>Point 440</b><br>Lat: 41.381309<br>Lon: 2.119981<br>Heading: 248.88<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.63 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381309,2.119981&heading=248.88&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381269, 2.119843]).addTo(map).bindPopup(`<b>Point 441</b><br>Lat: 41.381269<br>Lon: 2.119843<br>Heading: 249.23<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381269,2.119843&heading=249.23&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381232, 2.119713]).addTo(map).bindPopup(`<b>Point 442</b><br>Lat: 41.381232<br>Lon: 2.119713<br>Heading: 249.22<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.19 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381232,2.119713&heading=249.22&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381193, 2.119576]).addTo(map).bindPopup(`<b>Point 443</b><br>Lat: 41.381193<br>Lon: 2.119576<br>Heading: 248.80<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.03 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381193,2.119576&heading=248.80&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381154, 2.119442]).addTo(map).bindPopup(`<b>Point 444</b><br>Lat: 41.381154<br>Lon: 2.119442<br>Heading: 249.04<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 15.62 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381154,2.119442&heading=249.04&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381104, 2.119268]).addTo(map).bindPopup(`<b>Point 445</b><br>Lat: 41.381104<br>Lon: 2.119268<br>Heading: 249.92<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.23 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381104,2.119268&heading=249.92&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381073, 2.119155]).addTo(map).bindPopup(`<b>Point 446</b><br>Lat: 41.381073<br>Lon: 2.119155<br>Heading: 247.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.94 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381073,2.119155&heading=247.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.381030, 2.119020]).addTo(map).bindPopup(`<b>Point 447</b><br>Lat: 41.381030<br>Lon: 2.119020<br>Heading: 246.61<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.51 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.381030,2.119020&heading=246.61&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380993, 2.118906]).addTo(map).bindPopup(`<b>Point 448</b><br>Lat: 41.380993<br>Lon: 2.118906<br>Heading: 249.45<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 19.64 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380993,2.118906&heading=249.45&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380930, 2.118682]).addTo(map).bindPopup(`<b>Point 449</b><br>Lat: 41.380930<br>Lon: 2.118682<br>Heading: 217.88<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.87 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380930,2.118682&heading=217.88&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380821, 2.118569]).addTo(map).bindPopup(`<b>Point 450</b><br>Lat: 41.380821<br>Lon: 2.118569<br>Heading: 174.97<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.32 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380821,2.118569&heading=174.97&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380676, 2.118586]).addTo(map).bindPopup(`<b>Point 451</b><br>Lat: 41.380676<br>Lon: 2.118586<br>Heading: 136.10<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.42 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380676,2.118586&heading=136.10&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380591, 2.118695]).addTo(map).bindPopup(`<b>Point 452</b><br>Lat: 41.380591<br>Lon: 2.118695<br>Heading: 146.26<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.71 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380591,2.118695&heading=146.26&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380509, 2.118768]).addTo(map).bindPopup(`<b>Point 453</b><br>Lat: 41.380509<br>Lon: 2.118768<br>Heading: 153.54<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.66 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380509,2.118768&heading=153.54&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380408, 2.118835]).addTo(map).bindPopup(`<b>Point 454</b><br>Lat: 41.380408<br>Lon: 2.118835<br>Heading: 168.71<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.16 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380408,2.118835&heading=168.71&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380284, 2.118868]).addTo(map).bindPopup(`<b>Point 455</b><br>Lat: 41.380284<br>Lon: 2.118868<br>Heading: 168.51<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.41 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380284,2.118868&heading=168.51&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380140, 2.118907]).addTo(map).bindPopup(`<b>Point 456</b><br>Lat: 41.380140<br>Lon: 2.118907<br>Heading: 167.31<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.96 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380140,2.118907&heading=167.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379980, 2.118955]).addTo(map).bindPopup(`<b>Point 457</b><br>Lat: 41.379980<br>Lon: 2.118955<br>Heading: 160.52<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.50 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379980,2.118955&heading=160.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379840, 2.119021]).addTo(map).bindPopup(`<b>Point 458</b><br>Lat: 41.379840<br>Lon: 2.119021<br>Heading: 157.91<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 18.34 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379840,2.119021&heading=157.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379681, 2.119107]).addTo(map).bindPopup(`<b>Point 459</b><br>Lat: 41.379681<br>Lon: 2.119107<br>Heading: 155.58<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 19.17 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379681,2.119107&heading=155.58&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379524, 2.119202]).addTo(map).bindPopup(`<b>Point 460</b><br>Lat: 41.379524<br>Lon: 2.119202<br>Heading: 153.42<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.33 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379524,2.119202&heading=153.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379428, 2.119266]).addTo(map).bindPopup(`<b>Point 461</b><br>Lat: 41.379428<br>Lon: 2.119266<br>Heading: 144.48<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 18.31 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379428,2.119266&heading=144.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379284, 2.119403]).addTo(map).bindPopup(`<b>Point 462</b><br>Lat: 41.379284<br>Lon: 2.119403<br>Heading: 133.25<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.18 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379284,2.119403&heading=133.25&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379212, 2.119505]).addTo(map).bindPopup(`<b>Point 463</b><br>Lat: 41.379212<br>Lon: 2.119505<br>Heading: 130.05<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 19.31 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379212,2.119505&heading=130.05&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379101, 2.119681]).addTo(map).bindPopup(`<b>Point 464</b><br>Lat: 41.379101<br>Lon: 2.119681<br>Heading: 129.68<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.64 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379101,2.119681&heading=129.68&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379040, 2.119779]).addTo(map).bindPopup(`<b>Point 465</b><br>Lat: 41.379040<br>Lon: 2.119779<br>Heading: 125.07<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379040,2.119779&heading=125.07&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378981, 2.119891]).addTo(map).bindPopup(`<b>Point 466</b><br>Lat: 41.378981<br>Lon: 2.119891<br>Heading: 123.18<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.57 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378981,2.119891&heading=123.18&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378929, 2.119997]).addTo(map).bindPopup(`<b>Point 467</b><br>Lat: 41.378929<br>Lon: 2.119997<br>Heading: 123.90<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.73 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378929,2.119997&heading=123.90&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378870, 2.120114]).addTo(map).bindPopup(`<b>Point 468</b><br>Lat: 41.378870<br>Lon: 2.120114<br>Heading: 123.16<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.25 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378870,2.120114&heading=123.16&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378820, 2.120216]).addTo(map).bindPopup(`<b>Point 469</b><br>Lat: 41.378820<br>Lon: 2.120216<br>Heading: 123.81<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 20.16 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378820,2.120216&heading=123.81&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378720, 2.120415]).addTo(map).bindPopup(`<b>Point 470</b><br>Lat: 41.378720<br>Lon: 2.120415<br>Heading: 126.58<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 18.75 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378720,2.120415&heading=126.58&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378627, 2.120582]).addTo(map).bindPopup(`<b>Point 471</b><br>Lat: 41.378627<br>Lon: 2.120582<br>Heading: 138.37<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.40 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378627,2.120582&heading=138.37&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378524, 2.120704]).addTo(map).bindPopup(`<b>Point 472</b><br>Lat: 41.378524<br>Lon: 2.120704<br>Heading: 138.30<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.21 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378524,2.120704&heading=138.30&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378428, 2.120818]).addTo(map).bindPopup(`<b>Point 473</b><br>Lat: 41.378428<br>Lon: 2.120818<br>Heading: 138.07<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.70 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378428,2.120818&heading=138.07&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378357, 2.120903]).addTo(map).bindPopup(`<b>Point 474</b><br>Lat: 41.378357<br>Lon: 2.120903<br>Heading: 138.32<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.17 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378357,2.120903&heading=138.32&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378282, 2.120992]).addTo(map).bindPopup(`<b>Point 475</b><br>Lat: 41.378282<br>Lon: 2.120992<br>Heading: 141.44<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.61 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378282,2.120992&heading=141.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378186, 2.121094]).addTo(map).bindPopup(`<b>Point 476</b><br>Lat: 41.378186<br>Lon: 2.121094<br>Heading: 149.02<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 18.16 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378186,2.121094&heading=149.02&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378046, 2.121206]).addTo(map).bindPopup(`<b>Point 477</b><br>Lat: 41.378046<br>Lon: 2.121206<br>Heading: 149.28<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 19.89 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378046,2.121206&heading=149.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377897, 2.121324]).addTo(map).bindPopup(`<b>Point 478</b><br>Lat: 41.377897<br>Lon: 2.121324<br>Heading: 158.86<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 19.66 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377897,2.121324&heading=158.86&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377734, 2.121408]).addTo(map).bindPopup(`<b>Point 479</b><br>Lat: 41.377734<br>Lon: 2.121408<br>Heading: 157.59<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.81 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377734,2.121408&heading=157.59&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377643, 2.121458]).addTo(map).bindPopup(`<b>Point 480</b><br>Lat: 41.377643<br>Lon: 2.121458<br>Heading: 156.36<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.77 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377643,2.121458&heading=156.36&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377547, 2.121514]).addTo(map).bindPopup(`<b>Point 481</b><br>Lat: 41.377547<br>Lon: 2.121514<br>Heading: 168.52<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.40 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377547,2.121514&heading=168.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377462, 2.121537]).addTo(map).bindPopup(`<b>Point 482</b><br>Lat: 41.377462<br>Lon: 2.121537<br>Heading: 168.27<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.89 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377462,2.121537&heading=168.27&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377350, 2.121568]).addTo(map).bindPopup(`<b>Point 483</b><br>Lat: 41.377350<br>Lon: 2.121568<br>Heading: 180.51<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.85 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377350,2.121568&heading=180.51&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377265, 2.121567]).addTo(map).bindPopup(`<b>Point 484</b><br>Lat: 41.377265<br>Lon: 2.121567<br>Heading: 180.00<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.67 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377265,2.121567&heading=180.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377169, 2.121567]).addTo(map).bindPopup(`<b>Point 485</b><br>Lat: 41.377169<br>Lon: 2.121567<br>Heading: 180.50<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.34 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377169,2.121567&heading=180.50&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376997, 2.121565]).addTo(map).bindPopup(`<b>Point 486</b><br>Lat: 41.376997<br>Lon: 2.121565<br>Heading: 181.28<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.36 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376997,2.121565&heading=181.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376896, 2.121562]).addTo(map).bindPopup(`<b>Point 487</b><br>Lat: 41.376896<br>Lon: 2.121562<br>Heading: 180.99<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 19.22 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376896,2.121562&heading=180.99&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376723, 2.121558]).addTo(map).bindPopup(`<b>Point 488</b><br>Lat: 41.376723<br>Lon: 2.121558<br>Heading: 182.19<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.90 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376723,2.121558&heading=182.19&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376605, 2.121552]).addTo(map).bindPopup(`<b>Point 489</b><br>Lat: 41.376605<br>Lon: 2.121552<br>Heading: 181.48<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.05 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376605,2.121552&heading=181.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376518, 2.121549]).addTo(map).bindPopup(`<b>Point 490</b><br>Lat: 41.376518<br>Lon: 2.121549<br>Heading: 181.44<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376518,2.121549&heading=181.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376399, 2.121545]).addTo(map).bindPopup(`<b>Point 491</b><br>Lat: 41.376399<br>Lon: 2.121545<br>Heading: 179.43<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 16.53 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376399,2.121545&heading=179.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376249, 2.121547]).addTo(map).bindPopup(`<b>Point 492</b><br>Lat: 41.376249<br>Lon: 2.121547<br>Heading: 178.50<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.53 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376249,2.121547&heading=178.50&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376106, 2.121552]).addTo(map).bindPopup(`<b>Point 493</b><br>Lat: 41.376106<br>Lon: 2.121552<br>Heading: 170.09<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.45 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376106,2.121552&heading=170.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375960, 2.121586]).addTo(map).bindPopup(`<b>Point 494</b><br>Lat: 41.375960<br>Lon: 2.121586<br>Heading: 158.97<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.46 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375960,2.121586&heading=158.97&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375839, 2.121648]).addTo(map).bindPopup(`<b>Point 495</b><br>Lat: 41.375839<br>Lon: 2.121648<br>Heading: 162.35<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375839,2.121648&heading=162.35&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375747, 2.121687]).addTo(map).bindPopup(`<b>Point 496</b><br>Lat: 41.375747<br>Lon: 2.121687<br>Heading: 113.66<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.28 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375747,2.121687&heading=113.66&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375675, 2.121906]).addTo(map).bindPopup(`<b>Point 497</b><br>Lat: 41.375675<br>Lon: 2.121906<br>Heading: 90.55<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 11.68 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375675,2.121906&heading=90.55&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375674, 2.122044]).addTo(map).bindPopup(`<b>Point 498</b><br>Lat: 41.375674<br>Lon: 2.122044<br>Heading: 90.47<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.60 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375674,2.122044&heading=90.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375673, 2.122207]).addTo(map).bindPopup(`<b>Point 499</b><br>Lat: 41.375673<br>Lon: 2.122207<br>Heading: 90.61<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.52 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375673,2.122207&heading=90.61&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375672, 2.122333]).addTo(map).bindPopup(`<b>Point 500</b><br>Lat: 41.375672<br>Lon: 2.122333<br>Heading: 90.59<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375672,2.122333&heading=90.59&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375671, 2.122462]).addTo(map).bindPopup(`<b>Point 501</b><br>Lat: 41.375671<br>Lon: 2.122462<br>Heading: 91.45<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.27 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375671,2.122462&heading=91.45&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375668, 2.122620]).addTo(map).bindPopup(`<b>Point 502</b><br>Lat: 41.375668<br>Lon: 2.122620<br>Heading: 91.73<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.66 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375668,2.122620&heading=91.73&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375664, 2.122796]).addTo(map).bindPopup(`<b>Point 503</b><br>Lat: 41.375664<br>Lon: 2.122796<br>Heading: 91.79<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 17.79 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375664,2.122796&heading=91.79&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375659, 2.123009]).addTo(map).bindPopup(`<b>Point 504</b><br>Lat: 41.375659<br>Lon: 2.123009<br>Heading: 92.53<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.16 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375659,2.123009&heading=92.53&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375653, 2.123190]).addTo(map).bindPopup(`<b>Point 505</b><br>Lat: 41.375653<br>Lon: 2.123190<br>Heading: 91.76<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.44 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375653,2.123190&heading=91.76&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375649, 2.123363]).addTo(map).bindPopup(`<b>Point 506</b><br>Lat: 41.375649<br>Lon: 2.123363<br>Heading: 92.02<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.64 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375649,2.123363&heading=92.02&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375645, 2.123514]).addTo(map).bindPopup(`<b>Point 507</b><br>Lat: 41.375645<br>Lon: 2.123514<br>Heading: 91.51<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 17.54 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375645,2.123514&heading=91.51&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375641, 2.123716]).addTo(map).bindPopup(`<b>Point 508</b><br>Lat: 41.375641<br>Lon: 2.123716<br>Heading: 92.14<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.74 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375641,2.123716&heading=92.14&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375636, 2.123894]).addTo(map).bindPopup(`<b>Point 509</b><br>Lat: 41.375636<br>Lon: 2.123894<br>Heading: 91.52<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 17.10 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375636,2.123894&heading=91.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375632, 2.124095]).addTo(map).bindPopup(`<b>Point 510</b><br>Lat: 41.375632<br>Lon: 2.124095<br>Heading: 91.54<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.16 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375632,2.124095&heading=91.54&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375628, 2.124293]).addTo(map).bindPopup(`<b>Point 511</b><br>Lat: 41.375628<br>Lon: 2.124293<br>Heading: 91.95<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 16.36 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375628,2.124293&heading=91.95&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375623, 2.124489]).addTo(map).bindPopup(`<b>Point 512</b><br>Lat: 41.375623<br>Lon: 2.124489<br>Heading: 91.45<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.20 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375623,2.124489&heading=91.45&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375620, 2.124647]).addTo(map).bindPopup(`<b>Point 513</b><br>Lat: 41.375620<br>Lon: 2.124647<br>Heading: 91.78<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.81 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375620,2.124647&heading=91.78&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375617, 2.124776]).addTo(map).bindPopup(`<b>Point 514</b><br>Lat: 41.375617<br>Lon: 2.124776<br>Heading: 91.78<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.80 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375617,2.124776&heading=91.78&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375614, 2.124905]).addTo(map).bindPopup(`<b>Point 515</b><br>Lat: 41.375614<br>Lon: 2.124905<br>Heading: 91.26<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 10.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375614,2.124905&heading=91.26&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375612, 2.125026]).addTo(map).bindPopup(`<b>Point 516</b><br>Lat: 41.375612<br>Lon: 2.125026<br>Heading: 91.73<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 14.72 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375612,2.125026&heading=91.73&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375608, 2.125202]).addTo(map).bindPopup(`<b>Point 517</b><br>Lat: 41.375608<br>Lon: 2.125202<br>Heading: 91.68<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 15.49 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375608,2.125202&heading=91.68&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375604, 2.125384]).addTo(map).bindPopup(`<b>Point 518</b><br>Lat: 41.375604<br>Lon: 2.125384<br>Heading: 90.41<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 15.25 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375604,2.125384&heading=90.41&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375603, 2.125570]).addTo(map).bindPopup(`<b>Point 519</b><br>Lat: 41.375603<br>Lon: 2.125570<br>Heading: 90.44<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.46 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375603,2.125570&heading=90.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375602, 2.125743]).addTo(map).bindPopup(`<b>Point 520</b><br>Lat: 41.375602<br>Lon: 2.125743<br>Heading: 90.39<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.42 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375602,2.125743&heading=90.39&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375601, 2.125940]).addTo(map).bindPopup(`<b>Point 521</b><br>Lat: 41.375601<br>Lon: 2.125940<br>Heading: 90.81<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 15.76 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375601,2.125940&heading=90.81&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375599, 2.126129]).addTo(map).bindPopup(`<b>Point 522</b><br>Lat: 41.375599<br>Lon: 2.126129<br>Heading: 90.39<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.32 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375599,2.126129&heading=90.39&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375598, 2.126324]).addTo(map).bindPopup(`<b>Point 523</b><br>Lat: 41.375598<br>Lon: 2.126324<br>Heading: 91.88<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.86 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375598,2.126324&heading=91.88&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375593, 2.126527]).addTo(map).bindPopup(`<b>Point 524</b><br>Lat: 41.375593<br>Lon: 2.126527<br>Heading: 91.89<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.88 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375593,2.126527&heading=91.89&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375588, 2.126729]).addTo(map).bindPopup(`<b>Point 525</b><br>Lat: 41.375588<br>Lon: 2.126729<br>Heading: 91.82<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 17.56 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375588,2.126729&heading=91.82&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375583, 2.126939]).addTo(map).bindPopup(`<b>Point 526</b><br>Lat: 41.375583<br>Lon: 2.126939<br>Heading: 91.91<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 18.23 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375583,2.126939&heading=91.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375575, 2.127259]).addTo(map).bindPopup(`<b>Point 527</b><br>Lat: 41.375575<br>Lon: 2.127259<br>Heading: 92.21<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 17.30 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375575,2.127259&heading=92.21&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375569, 2.127466]).addTo(map).bindPopup(`<b>Point 528</b><br>Lat: 41.375569<br>Lon: 2.127466<br>Heading: 91.11<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 17.16 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375569,2.127466&heading=91.11&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375566, 2.127672]).addTo(map).bindPopup(`<b>Point 529</b><br>Lat: 41.375566<br>Lon: 2.127672<br>Heading: 91.21<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 15.78 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375566,2.127672&heading=91.21&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375563, 2.127861]).addTo(map).bindPopup(`<b>Point 530</b><br>Lat: 41.375563<br>Lon: 2.127861<br>Heading: 91.09<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 17.51 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375563,2.127861&heading=91.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375560, 2.128071]).addTo(map).bindPopup(`<b>Point 531</b><br>Lat: 41.375560<br>Lon: 2.128071<br>Heading: 91.44<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 17.70 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375560,2.128071&heading=91.44&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375556, 2.128283]).addTo(map).bindPopup(`<b>Point 532</b><br>Lat: 41.375556<br>Lon: 2.128283<br>Heading: 91.64<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 19.46 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375556,2.128283&heading=91.64&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375551, 2.128516]).addTo(map).bindPopup(`<b>Point 533</b><br>Lat: 41.375551<br>Lon: 2.128516<br>Heading: 91.77<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 18.02 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375551,2.128516&heading=91.77&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375546, 2.128732]).addTo(map).bindPopup(`<b>Point 534</b><br>Lat: 41.375546<br>Lon: 2.128732<br>Heading: 91.47<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 17.38 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375546,2.128732&heading=91.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375542, 2.128940]).addTo(map).bindPopup(`<b>Point 535</b><br>Lat: 41.375542<br>Lon: 2.128940<br>Heading: 91.62<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 15.71 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375542,2.128940&heading=91.62&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375538, 2.129128]).addTo(map).bindPopup(`<b>Point 536</b><br>Lat: 41.375538<br>Lon: 2.129128<br>Heading: 91.88<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.16 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375538,2.129128&heading=91.88&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375535, 2.129250]).addTo(map).bindPopup(`<b>Point 537</b><br>Lat: 41.375535<br>Lon: 2.129250<br>Heading: 92.31<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.59 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375535,2.129250&heading=92.31&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375529, 2.129448]).addTo(map).bindPopup(`<b>Point 538</b><br>Lat: 41.375529<br>Lon: 2.129448<br>Heading: 91.94<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.12 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375529,2.129448&heading=91.94&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375525, 2.129605]).addTo(map).bindPopup(`<b>Point 539</b><br>Lat: 41.375525<br>Lon: 2.129605<br>Heading: 93.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.63 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375525,2.129605&heading=93.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375520, 2.129732]).addTo(map).bindPopup(`<b>Point 540</b><br>Lat: 41.375520<br>Lon: 2.129732<br>Heading: 92.28<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.18 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375520,2.129732&heading=92.28&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375516, 2.129866]).addTo(map).bindPopup(`<b>Point 541</b><br>Lat: 41.375516<br>Lon: 2.129866<br>Heading: 92.37<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.41 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375516,2.129866&heading=92.37&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375511, 2.130027]).addTo(map).bindPopup(`<b>Point 542</b><br>Lat: 41.375511<br>Lon: 2.130027<br>Heading: 90.43<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.77 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375511,2.130027&heading=90.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375510, 2.130204]).addTo(map).bindPopup(`<b>Point 543</b><br>Lat: 41.375510<br>Lon: 2.130204<br>Heading: 90.56<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.41 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375510,2.130204&heading=90.56&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375509, 2.130340]).addTo(map).bindPopup(`<b>Point 544</b><br>Lat: 41.375509<br>Lon: 2.130340<br>Heading: 90.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.19 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375509,2.130340&heading=90.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375509, 2.130452]).addTo(map).bindPopup(`<b>Point 545</b><br>Lat: 41.375509<br>Lon: 2.130452<br>Heading: 90.83<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.53 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375509,2.130452&heading=90.83&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375507, 2.130637]).addTo(map).bindPopup(`<b>Point 546</b><br>Lat: 41.375507<br>Lon: 2.130637<br>Heading: 98.13<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.24 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375507,2.130637&heading=98.13&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375489, 2.130805]).addTo(map).bindPopup(`<b>Point 547</b><br>Lat: 41.375489<br>Lon: 2.130805<br>Heading: 93.96<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 17.74 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375489,2.130805&heading=93.96&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375478, 2.131017]).addTo(map).bindPopup(`<b>Point 548</b><br>Lat: 41.375478<br>Lon: 2.131017<br>Heading: 91.53<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.50 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375478,2.131017&heading=91.53&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375474, 2.131217]).addTo(map).bindPopup(`<b>Point 549</b><br>Lat: 41.375474<br>Lon: 2.131217<br>Heading: 91.18<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.33 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375474,2.131217&heading=91.18&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375471, 2.131411]).addTo(map).bindPopup(`<b>Point 550</b><br>Lat: 41.375471<br>Lon: 2.131411<br>Heading: 94.01<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.75 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375471,2.131411&heading=94.01&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 551</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.68 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 552</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 553</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.48 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 554</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.75 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 555</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.68 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 556</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 557</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.48 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 558</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.75 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 559</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.68 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 560</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 561</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.48 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 562</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.75 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 563</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.68 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 564</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 565</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 0.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.48 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375469, 2.131449]).addTo(map).bindPopup(`<b>Point 566</b><br>Lat: 41.375469<br>Lon: 2.131449<br>Heading: 91.84<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.82 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375469,2.131449&heading=91.84&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375464, 2.131656]).addTo(map).bindPopup(`<b>Point 567</b><br>Lat: 41.375464<br>Lon: 2.131656<br>Heading: 179.52<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.02 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375464,2.131656&heading=179.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375018, 2.131661]).addTo(map).bindPopup(`<b>Point 568</b><br>Lat: 41.375018<br>Lon: 2.131661<br>Heading: 176.98<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.84 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375018,2.131661&heading=176.98&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 569</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.73 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 570</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 571</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.93 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 572</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.55 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 573</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 574</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.84 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 575</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.73 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 576</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 577</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.93 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 578</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.55 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 579</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 580</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.84 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 581</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.73 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 582</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 583</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.93 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 584</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.55 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 585</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 586</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.84 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 587</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.73 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 588</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 589</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.93 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 590</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.55 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 591</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 592</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.84 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 593</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.73 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 594</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 595</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.93 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 596</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.55 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 597</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 598</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.84 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 599</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.73 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 600</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 601</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.93 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 602</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.55 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 603</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374705, 2.131683]).addTo(map).bindPopup(`<b>Point 604</b><br>Lat: 41.374705<br>Lon: 2.131683<br>Heading: 78.41<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.78 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374705,2.131683&heading=78.41&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374715, 2.131748]).addTo(map).bindPopup(`<b>Point 605</b><br>Lat: 41.374715<br>Lon: 2.131748<br>Heading: 168.54<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374715,2.131748&heading=168.54&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374230, 2.131879]).addTo(map).bindPopup(`<b>Point 606</b><br>Lat: 41.374230<br>Lon: 2.131879<br>Heading: 189.04<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.53 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374230,2.131879&heading=189.04&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374098, 2.131851]).addTo(map).bindPopup(`<b>Point 607</b><br>Lat: 41.374098<br>Lon: 2.131851<br>Heading: 257.39<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.36 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374098,2.131851&heading=257.39&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374074, 2.131708]).addTo(map).bindPopup(`<b>Point 608</b><br>Lat: 41.374074<br>Lon: 2.131708<br>Heading: 257.76<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374074,2.131708&heading=257.76&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374053, 2.131579]).addTo(map).bindPopup(`<b>Point 609</b><br>Lat: 41.374053<br>Lon: 2.131579<br>Heading: 257.48<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.27 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374053,2.131579&heading=257.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374033, 2.131459]).addTo(map).bindPopup(`<b>Point 610</b><br>Lat: 41.374033<br>Lon: 2.131459<br>Heading: 258.06<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.43 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374033,2.131459&heading=258.06&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374010, 2.131314]).addTo(map).bindPopup(`<b>Point 611</b><br>Lat: 41.374010<br>Lon: 2.131314<br>Heading: 257.48<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.38 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374010,2.131314&heading=257.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.373988, 2.131182]).addTo(map).bindPopup(`<b>Point 612</b><br>Lat: 41.373988<br>Lon: 2.131182<br>Heading: 336.01<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 16.41 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.373988,2.131182&heading=336.01&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374219, 2.131045]).addTo(map).bindPopup(`<b>Point 613</b><br>Lat: 41.374219<br>Lon: 2.131045<br>Heading: 343.56<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.57 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374219,2.131045&heading=343.56&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374336, 2.130999]).addTo(map).bindPopup(`<b>Point 614</b><br>Lat: 41.374336<br>Lon: 2.130999<br>Heading: 343.98<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.25 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374336,2.130999&heading=343.98&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374451, 2.130955]).addTo(map).bindPopup(`<b>Point 615</b><br>Lat: 41.374451<br>Lon: 2.130955<br>Heading: 343.79<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.96 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374451,2.130955&heading=343.79&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374562, 2.130912]).addTo(map).bindPopup(`<b>Point 616</b><br>Lat: 41.374562<br>Lon: 2.130912<br>Heading: 346.84<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.53 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374562,2.130912&heading=346.84&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374684, 2.130874]).addTo(map).bindPopup(`<b>Point 617</b><br>Lat: 41.374684<br>Lon: 2.130874<br>Heading: 348.08<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 14.56 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374684,2.130874&heading=348.08&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374812, 2.130838]).addTo(map).bindPopup(`<b>Point 618</b><br>Lat: 41.374812<br>Lon: 2.130838<br>Heading: 347.90<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.90 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374812,2.130838&heading=347.90&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.374917, 2.130808]).addTo(map).bindPopup(`<b>Point 619</b><br>Lat: 41.374917<br>Lon: 2.130808<br>Heading: 347.55<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.69 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.374917,2.130808&heading=347.55&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375019, 2.130778]).addTo(map).bindPopup(`<b>Point 620</b><br>Lat: 41.375019<br>Lon: 2.130778<br>Heading: 347.96<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.81 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375019,2.130778&heading=347.96&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375114, 2.130751]).addTo(map).bindPopup(`<b>Point 621</b><br>Lat: 41.375114<br>Lon: 2.130751<br>Heading: 347.90<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.94 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375114,2.130751&heading=347.90&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375219, 2.130721]).addTo(map).bindPopup(`<b>Point 622</b><br>Lat: 41.375219<br>Lon: 2.130721<br>Heading: 348.13<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.28 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375219,2.130721&heading=348.13&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375319, 2.130693]).addTo(map).bindPopup(`<b>Point 623</b><br>Lat: 41.375319<br>Lon: 2.130693<br>Heading: 348.43<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.10 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375319,2.130693&heading=348.43&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375418, 2.130666]).addTo(map).bindPopup(`<b>Point 624</b><br>Lat: 41.375418<br>Lon: 2.130666<br>Heading: 353.27<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.74 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375418,2.130666&heading=353.27&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375507, 2.130652]).addTo(map).bindPopup(`<b>Point 625</b><br>Lat: 41.375507<br>Lon: 2.130652<br>Heading: 270.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375507,2.130652&heading=270.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375507, 2.130572]).addTo(map).bindPopup(`<b>Point 626</b><br>Lat: 41.375507<br>Lon: 2.130572<br>Heading: 271.18<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.93 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375507,2.130572&heading=271.18&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375509, 2.130443]).addTo(map).bindPopup(`<b>Point 627</b><br>Lat: 41.375509<br>Lon: 2.130443<br>Heading: 270.00<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.70 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375509,2.130443&heading=270.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375509, 2.130268]).addTo(map).bindPopup(`<b>Point 628</b><br>Lat: 41.375509<br>Lon: 2.130268<br>Heading: 270.85<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.78 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375509,2.130268&heading=270.85&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375511, 2.130089]).addTo(map).bindPopup(`<b>Point 629</b><br>Lat: 41.375511<br>Lon: 2.130089<br>Heading: 271.70<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 18.56 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375511,2.130089&heading=271.70&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375516, 2.129864]).addTo(map).bindPopup(`<b>Point 630</b><br>Lat: 41.375516<br>Lon: 2.129864<br>Heading: 272.54<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 17.52 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375516,2.129864&heading=272.54&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375523, 2.129654]).addTo(map).bindPopup(`<b>Point 631</b><br>Lat: 41.375523<br>Lon: 2.129654<br>Heading: 272.29<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.71 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375523,2.129654&heading=272.29&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375529, 2.129454]).addTo(map).bindPopup(`<b>Point 632</b><br>Lat: 41.375529<br>Lon: 2.129454<br>Heading: 272.50<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.63 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375529,2.129454&heading=272.50&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375533, 2.129332]).addTo(map).bindPopup(`<b>Point 633</b><br>Lat: 41.375533<br>Lon: 2.129332<br>Heading: 271.96<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 15.95 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375533,2.129332&heading=271.96&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375538, 2.129137]).addTo(map).bindPopup(`<b>Point 634</b><br>Lat: 41.375538<br>Lon: 2.129137<br>Heading: 271.57<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.20 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375538,2.129137&heading=271.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375542, 2.128943]).addTo(map).bindPopup(`<b>Point 635</b><br>Lat: 41.375542<br>Lon: 2.128943<br>Heading: 271.57<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.23 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375542,2.128943&heading=271.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375546, 2.128748]).addTo(map).bindPopup(`<b>Point 636</b><br>Lat: 41.375546<br>Lon: 2.128748<br>Heading: 271.81<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 17.67 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375546,2.128748&heading=271.81&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375551, 2.128537]).addTo(map).bindPopup(`<b>Point 637</b><br>Lat: 41.375551<br>Lon: 2.128537<br>Heading: 271.52<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.80 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375551,2.128537&heading=271.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375555, 2.128336]).addTo(map).bindPopup(`<b>Point 638</b><br>Lat: 41.375555<br>Lon: 2.128336<br>Heading: 271.53<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 17.23 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375555,2.128336&heading=271.53&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375559, 2.128136]).addTo(map).bindPopup(`<b>Point 639</b><br>Lat: 41.375559<br>Lon: 2.128136<br>Heading: 271.16<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.05 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375559,2.128136&heading=271.16&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375562, 2.127938]).addTo(map).bindPopup(`<b>Point 640</b><br>Lat: 41.375562<br>Lon: 2.127938<br>Heading: 271.23<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 20.65 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375562,2.127938&heading=271.23&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375566, 2.127690]).addTo(map).bindPopup(`<b>Point 641</b><br>Lat: 41.375566<br>Lon: 2.127690<br>Heading: 270.86<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.89 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375566,2.127690&heading=270.86&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375568, 2.127512]).addTo(map).bindPopup(`<b>Point 642</b><br>Lat: 41.375568<br>Lon: 2.127512<br>Heading: 271.75<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.26 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375568,2.127512&heading=271.75&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375572, 2.127338]).addTo(map).bindPopup(`<b>Point 643</b><br>Lat: 41.375572<br>Lon: 2.127338<br>Heading: 311.42<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.66 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375572,2.127338&heading=311.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375666, 2.127196]).addTo(map).bindPopup(`<b>Point 644</b><br>Lat: 41.375666<br>Lon: 2.127196<br>Heading: 340.70<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.60 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375666,2.127196&heading=340.70&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375756, 2.127154]).addTo(map).bindPopup(`<b>Point 645</b><br>Lat: 41.375756<br>Lon: 2.127154<br>Heading: 340.85<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 15.80 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375756,2.127154&heading=340.85&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375890, 2.127092]).addTo(map).bindPopup(`<b>Point 646</b><br>Lat: 41.375890<br>Lon: 2.127092<br>Heading: 340.84<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.66 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375890,2.127092&heading=340.84&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.375998, 2.127042]).addTo(map).bindPopup(`<b>Point 647</b><br>Lat: 41.375998<br>Lon: 2.127042<br>Heading: 340.99<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.58 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.375998,2.127042&heading=340.99&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376096, 2.126997]).addTo(map).bindPopup(`<b>Point 648</b><br>Lat: 41.376096<br>Lon: 2.126997<br>Heading: 340.97<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.22 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376096,2.126997&heading=340.97&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376183, 2.126957]).addTo(map).bindPopup(`<b>Point 649</b><br>Lat: 41.376183<br>Lon: 2.126957<br>Heading: 341.17<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.95 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376183,2.126957&heading=341.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376293, 2.126907]).addTo(map).bindPopup(`<b>Point 650</b><br>Lat: 41.376293<br>Lon: 2.126907<br>Heading: 340.81<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.49 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376293,2.126907&heading=340.81&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376390, 2.126862]).addTo(map).bindPopup(`<b>Point 651</b><br>Lat: 41.376390<br>Lon: 2.126862<br>Heading: 340.55<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 14.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376390,2.126862&heading=340.55&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376509, 2.126806]).addTo(map).bindPopup(`<b>Point 652</b><br>Lat: 41.376509<br>Lon: 2.126806<br>Heading: 340.81<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.44 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376509,2.126806&heading=340.81&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376606, 2.126761]).addTo(map).bindPopup(`<b>Point 653</b><br>Lat: 41.376606<br>Lon: 2.126761<br>Heading: 341.56<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.79 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376606,2.126761&heading=341.56&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376696, 2.126721]).addTo(map).bindPopup(`<b>Point 654</b><br>Lat: 41.376696<br>Lon: 2.126721<br>Heading: 341.42<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.00 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376696,2.126721&heading=341.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376792, 2.126678]).addTo(map).bindPopup(`<b>Point 655</b><br>Lat: 41.376792<br>Lon: 2.126678<br>Heading: 342.17<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.41 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376792,2.126678&heading=342.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376890, 2.126636]).addTo(map).bindPopup(`<b>Point 656</b><br>Lat: 41.376890<br>Lon: 2.126636<br>Heading: 342.17<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.71 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376890,2.126636&heading=342.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.376981, 2.126597]).addTo(map).bindPopup(`<b>Point 657</b><br>Lat: 41.376981<br>Lon: 2.126597<br>Heading: 342.11<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.376981,2.126597&heading=342.11&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377067, 2.126560]).addTo(map).bindPopup(`<b>Point 658</b><br>Lat: 41.377067<br>Lon: 2.126560<br>Heading: 342.37<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.96 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377067,2.126560&heading=342.37&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377178, 2.126513]).addTo(map).bindPopup(`<b>Point 659</b><br>Lat: 41.377178<br>Lon: 2.126513<br>Heading: 341.99<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.56 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377178,2.126513&heading=341.99&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377268, 2.126474]).addTo(map).bindPopup(`<b>Point 660</b><br>Lat: 41.377268<br>Lon: 2.126474<br>Heading: 340.78<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.65 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377268,2.126474&heading=340.78&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377367, 2.126428]).addTo(map).bindPopup(`<b>Point 661</b><br>Lat: 41.377367<br>Lon: 2.126428<br>Heading: 339.78<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.77 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377367,2.126428&heading=339.78&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377475, 2.126375]).addTo(map).bindPopup(`<b>Point 662</b><br>Lat: 41.377475<br>Lon: 2.126375<br>Heading: 339.24<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.42 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377475,2.126375&heading=339.24&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377570, 2.126327]).addTo(map).bindPopup(`<b>Point 663</b><br>Lat: 41.377570<br>Lon: 2.126327<br>Heading: 339.65<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.54 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377570,2.126327&heading=339.65&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377659, 2.126283]).addTo(map).bindPopup(`<b>Point 664</b><br>Lat: 41.377659<br>Lon: 2.126283<br>Heading: 339.22<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.83 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377659,2.126283&heading=339.22&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377748, 2.126238]).addTo(map).bindPopup(`<b>Point 665</b><br>Lat: 41.377748<br>Lon: 2.126238<br>Heading: 48.30<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.86 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377748,2.126238&heading=48.30&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377865, 2.126413]).addTo(map).bindPopup(`<b>Point 666</b><br>Lat: 41.377865<br>Lon: 2.126413<br>Heading: 52.29<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.89 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377865,2.126413&heading=52.29&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377941, 2.126544]).addTo(map).bindPopup(`<b>Point 667</b><br>Lat: 41.377941<br>Lon: 2.126544<br>Heading: 52.30<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 10.55 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377941,2.126544&heading=52.30&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.377999, 2.126644]).addTo(map).bindPopup(`<b>Point 668</b><br>Lat: 41.377999<br>Lon: 2.126644<br>Heading: 52.46<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.40 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.377999,2.126644&heading=52.46&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378078, 2.126781]).addTo(map).bindPopup(`<b>Point 669</b><br>Lat: 41.378078<br>Lon: 2.126781<br>Heading: 52.46<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.46 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378078,2.126781&heading=52.46&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378157, 2.126918]).addTo(map).bindPopup(`<b>Point 670</b><br>Lat: 41.378157<br>Lon: 2.126918<br>Heading: 53.59<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 16.55 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378157,2.126918&heading=53.59&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378245, 2.127077]).addTo(map).bindPopup(`<b>Point 671</b><br>Lat: 41.378245<br>Lon: 2.127077<br>Heading: 53.93<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 16.65 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378245,2.127077&heading=53.93&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378333, 2.127238]).addTo(map).bindPopup(`<b>Point 672</b><br>Lat: 41.378333<br>Lon: 2.127238<br>Heading: 54.09<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 17.78 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378333,2.127238&heading=54.09&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378427, 2.127411]).addTo(map).bindPopup(`<b>Point 673</b><br>Lat: 41.378427<br>Lon: 2.127411<br>Heading: 53.20<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 17.68 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378427,2.127411&heading=53.20&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378523, 2.127582]).addTo(map).bindPopup(`<b>Point 674</b><br>Lat: 41.378523<br>Lon: 2.127582<br>Heading: 52.08<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 13.80 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378523,2.127582&heading=52.08&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378599, 2.127712]).addTo(map).bindPopup(`<b>Point 675</b><br>Lat: 41.378599<br>Lon: 2.127712<br>Heading: 54.21<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.07 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378599,2.127712&heading=54.21&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378685, 2.127871]).addTo(map).bindPopup(`<b>Point 676</b><br>Lat: 41.378685<br>Lon: 2.127871<br>Heading: 54.02<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 14.28 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378685,2.127871&heading=54.02&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378758, 2.128005]).addTo(map).bindPopup(`<b>Point 677</b><br>Lat: 41.378758<br>Lon: 2.128005<br>Heading: 347.80<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.39 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378758,2.128005&heading=347.80&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.378928, 2.127956]).addTo(map).bindPopup(`<b>Point 678</b><br>Lat: 41.378928<br>Lon: 2.127956<br>Heading: 325.38<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.24 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.378928,2.127956&heading=325.38&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379028, 2.127864]).addTo(map).bindPopup(`<b>Point 679</b><br>Lat: 41.379028<br>Lon: 2.127864<br>Heading: 325.57<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379028,2.127864&heading=325.57&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379132, 2.127769]).addTo(map).bindPopup(`<b>Point 680</b><br>Lat: 41.379132<br>Lon: 2.127769<br>Heading: 325.97<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.40 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379132,2.127769&heading=325.97&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379232, 2.127679]).addTo(map).bindPopup(`<b>Point 681</b><br>Lat: 41.379232<br>Lon: 2.127679<br>Heading: 326.23<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.50 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379232,2.127679&heading=326.23&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379333, 2.127589]).addTo(map).bindPopup(`<b>Point 682</b><br>Lat: 41.379333<br>Lon: 2.127589<br>Heading: 322.07<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379333,2.127589&heading=322.07&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379437, 2.127481]).addTo(map).bindPopup(`<b>Point 683</b><br>Lat: 41.379437<br>Lon: 2.127481<br>Heading: 311.15<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.58 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379437,2.127481&heading=311.15&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379517, 2.127359]).addTo(map).bindPopup(`<b>Point 684</b><br>Lat: 41.379517<br>Lon: 2.127359<br>Heading: 311.50<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 13.56 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379517,2.127359&heading=311.50&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379598, 2.127237]).addTo(map).bindPopup(`<b>Point 685</b><br>Lat: 41.379598<br>Lon: 2.127237<br>Heading: 311.11<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.54 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379598,2.127237&heading=311.11&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379672, 2.127124]).addTo(map).bindPopup(`<b>Point 686</b><br>Lat: 41.379672<br>Lon: 2.127124<br>Heading: 311.33<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.79 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379672,2.127124&heading=311.33&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379736, 2.127027]).addTo(map).bindPopup(`<b>Point 687</b><br>Lat: 41.379736<br>Lon: 2.127027<br>Heading: 311.47<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.61 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379736,2.127027&heading=311.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379799, 2.126932]).addTo(map).bindPopup(`<b>Point 688</b><br>Lat: 41.379799<br>Lon: 2.126932<br>Heading: 311.29<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.38 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379799,2.126932&heading=311.29&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379884, 2.126803]).addTo(map).bindPopup(`<b>Point 689</b><br>Lat: 41.379884<br>Lon: 2.126803<br>Heading: 310.61<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 14.01 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379884,2.126803&heading=310.61&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379967, 2.126674]).addTo(map).bindPopup(`<b>Point 690</b><br>Lat: 41.379967<br>Lon: 2.126674<br>Heading: 308.53<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 16.47 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379967,2.126674&heading=308.53&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380059, 2.126520]).addTo(map).bindPopup(`<b>Point 691</b><br>Lat: 41.380059<br>Lon: 2.126520<br>Heading: 308.07<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 12.08 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380059,2.126520&heading=308.07&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380126, 2.126406]).addTo(map).bindPopup(`<b>Point 692</b><br>Lat: 41.380126<br>Lon: 2.126406<br>Heading: 308.55<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 10.37 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380126,2.126406&heading=308.55&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380184, 2.126309]).addTo(map).bindPopup(`<b>Point 693</b><br>Lat: 41.380184<br>Lon: 2.126309<br>Heading: 245.32<br>Left Side: 8.0 m<br>Right Side: 8.0 m<br>Distance: 11.85 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380184,2.126309&heading=245.32&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380144, 2.126193]).addTo(map).bindPopup(`<b>Point 694</b><br>Lat: 41.380144<br>Lon: 2.126193<br>Heading: 229.73<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.15 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380144,2.126193&heading=229.73&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380062, 2.126064]).addTo(map).bindPopup(`<b>Point 695</b><br>Lat: 41.380062<br>Lon: 2.126064<br>Heading: 260.48<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.60 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380062,2.126064&heading=260.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.380042, 2.125905]).addTo(map).bindPopup(`<b>Point 696</b><br>Lat: 41.380042<br>Lon: 2.125905<br>Heading: 238.30<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.380042,2.125905&heading=238.30&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379985, 2.125782]).addTo(map).bindPopup(`<b>Point 697</b><br>Lat: 41.379985<br>Lon: 2.125782<br>Heading: 228.11<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379985,2.125782&heading=228.11&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379913, 2.125675]).addTo(map).bindPopup(`<b>Point 698</b><br>Lat: 41.379913<br>Lon: 2.125675<br>Heading: 228.81<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.95 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379913,2.125675&heading=228.81&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379848, 2.125576]).addTo(map).bindPopup(`<b>Point 699</b><br>Lat: 41.379848<br>Lon: 2.125576<br>Heading: 229.55<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.17 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379848,2.125576&heading=229.55&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379777, 2.125465]).addTo(map).bindPopup(`<b>Point 700</b><br>Lat: 41.379777<br>Lon: 2.125465<br>Heading: 229.18<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.85 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379777,2.125465&heading=229.18&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379707, 2.125357]).addTo(map).bindPopup(`<b>Point 701</b><br>Lat: 41.379707<br>Lon: 2.125357<br>Heading: 231.17<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.75 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379707,2.125357&heading=231.17&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379646, 2.125256]).addTo(map).bindPopup(`<b>Point 702</b><br>Lat: 41.379646<br>Lon: 2.125256<br>Heading: 232.14<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.16 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379646,2.125256&heading=232.14&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379590, 2.125160]).addTo(map).bindPopup(`<b>Point 703</b><br>Lat: 41.379590<br>Lon: 2.125160<br>Heading: 231.68<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.05 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379590,2.125160&heading=231.68&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379523, 2.125047]).addTo(map).bindPopup(`<b>Point 704</b><br>Lat: 41.379523<br>Lon: 2.125047<br>Heading: 231.78<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.35 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379523,2.125047&heading=231.78&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379510, 2.125025]).addTo(map).bindPopup(`<b>Point 705</b><br>Lat: 41.379510<br>Lon: 2.125025<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.32 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379510,2.125025&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379510, 2.125025]).addTo(map).bindPopup(`<b>Point 706</b><br>Lat: 41.379510<br>Lon: 2.125025<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.35 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379510,2.125025&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379510, 2.125025]).addTo(map).bindPopup(`<b>Point 707</b><br>Lat: 41.379510<br>Lon: 2.125025<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.32 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379510,2.125025&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([41.379510, 2.125025]).addTo(map).bindPopup(`<b>Point 708</b><br>Lat: 41.379510<br>Lon: 2.125025<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.32 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=41.379510,2.125025&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
        </script>
        </body>
        </html>
        