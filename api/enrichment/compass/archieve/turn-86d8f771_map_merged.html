
        <!DOCTYPE html>
        <html>
        <head>
            <title>GPS Rectangles Map</title>
            <meta charset="utf-8" />
            <style> #map { height: 100vh; width: 100%; } </style>
            <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
            <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
        </head>
        <body>
        <div id="map"></div>
        <script>
            var map = L.map('map').setView([32.924726, -97.085924], 18);
            <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 22,
                attribution: '&copy; OpenStreetMap contributors'
            }).addTo(map);

            L.polygon([[32.924726, -97.085924], [32.924590, -97.085983], [32.924645, -97.086160], [32.924916, -97.086042], [32.924862, -97.085865], [32.924726, -97.085924]], {color: 'red'}).addTo(map);
L.polygon([[32.924780, -97.086100], [32.924644, -97.086158], [32.924697, -97.086330], [32.924968, -97.086213], [32.924916, -97.086042], [32.924780, -97.086100]], {color: 'red'}).addTo(map);
L.polygon([[32.924832, -97.086271], [32.924698, -97.086334], [32.924771, -97.086554], [32.925039, -97.086429], [32.924966, -97.086208], [32.924832, -97.086271]], {color: 'red'}).addTo(map);
L.polygon([[32.924905, -97.086492], [32.924771, -97.086555], [32.924833, -97.086740], [32.925101, -97.086615], [32.925039, -97.086429], [32.924905, -97.086492]], {color: 'red'}).addTo(map);
L.polygon([[32.924963, -97.086667], [32.924833, -97.086741], [32.924901, -97.086909], [32.925161, -97.086761], [32.925093, -97.086593], [32.924963, -97.086667]], {color: 'red'}).addTo(map);
L.polygon([[32.925034, -97.086844], [32.924904, -97.086918], [32.924963, -97.087064], [32.925223, -97.086916], [32.925164, -97.086770], [32.925034, -97.086844]], {color: 'red'}).addTo(map);
L.polygon([[32.925093, -97.086990], [32.924933, -97.087089], [32.925001, -97.087243], [32.925321, -97.087045], [32.925253, -97.086891], [32.925093, -97.086990]], {color: 'red'}).addTo(map);
L.polygon([[32.925159, -97.087141], [32.925007, -97.087257], [32.925079, -97.087389], [32.925382, -97.087157], [32.925311, -97.087025], [32.925159, -97.087141]], {color: 'red'}).addTo(map);
L.polygon([[32.925230, -97.087272], [32.925079, -97.087388], [32.925140, -97.087501], [32.925443, -97.087268], [32.925381, -97.087156], [32.925230, -97.087272]], {color: 'red'}).addTo(map);
L.polygon([[32.925291, -97.087384], [32.925139, -97.087499], [32.925200, -97.087613], [32.925504, -97.087383], [32.925443, -97.087269], [32.925291, -97.087384]], {color: 'red'}).addTo(map);
L.polygon([[32.925352, -97.087498], [32.925187, -97.087585], [32.925252, -97.087759], [32.925582, -97.087586], [32.925517, -97.087411], [32.925352, -97.087498]], {color: 'red'}).addTo(map);
L.polygon([[32.925427, -97.087700], [32.925391, -97.087702], [32.925396, -97.087823], [32.925468, -97.087818], [32.925463, -97.087698], [32.925427, -97.087700]], {color: 'red'}).addTo(map);
L.polygon([[32.925432, -97.087814], [32.925396, -97.087817], [32.925403, -97.087941], [32.925475, -97.087936], [32.925468, -97.087811], [32.925432, -97.087814]], {color: 'red'}).addTo(map);
L.polygon([[32.925438, -97.087923], [32.925402, -97.087926], [32.925409, -97.088037], [32.925481, -97.088030], [32.925474, -97.087920], [32.925438, -97.087923]], {color: 'red'}).addTo(map);
L.polygon([[32.925445, -97.088034], [32.925442, -97.088077], [32.925543, -97.088086], [32.925548, -97.088001], [32.925448, -97.087991], [32.925445, -97.088034]], {color: 'red'}).addTo(map);
L.polygon([[32.925657, -97.088054], [32.925515, -97.088024], [32.925495, -97.088160], [32.925779, -97.088219], [32.925799, -97.088084], [32.925657, -97.088054]], {color: 'red'}).addTo(map);
L.polygon([[32.925622, -97.088291], [32.925601, -97.088256], [32.925490, -97.088350], [32.925532, -97.088420], [32.925643, -97.088326], [32.925622, -97.088291]], {color: 'red'}).addTo(map);
L.polygon([[32.925521, -97.088377], [32.925458, -97.088273], [32.925325, -97.088387], [32.925452, -97.088595], [32.925584, -97.088481], [32.925521, -97.088377]], {color: 'red'}).addTo(map);
L.polygon([[32.925390, -97.088490], [32.925326, -97.088386], [32.925246, -97.088456], [32.925373, -97.088663], [32.925454, -97.088594], [32.925390, -97.088490]], {color: 'red'}).addTo(map);
L.polygon([[32.925309, -97.088560], [32.925245, -97.088456], [32.925149, -97.088539], [32.925276, -97.088747], [32.925373, -97.088664], [32.925309, -97.088560]], {color: 'red'}).addTo(map);
L.polygon([[32.925213, -97.088643], [32.925150, -97.088539], [32.925059, -97.088616], [32.925185, -97.088825], [32.925276, -97.088747], [32.925213, -97.088643]], {color: 'red'}).addTo(map);
L.polygon([[32.925123, -97.088720], [32.925060, -97.088616], [32.924965, -97.088697], [32.925092, -97.088905], [32.925186, -97.088824], [32.925123, -97.088720]], {color: 'red'}).addTo(map);
L.polygon([[32.925029, -97.088801], [32.924965, -97.088698], [32.924885, -97.088767], [32.925013, -97.088974], [32.925093, -97.088904], [32.925029, -97.088801]], {color: 'red'}).addTo(map);
L.polygon([[32.924949, -97.088871], [32.924886, -97.088767], [32.924790, -97.088849], [32.924916, -97.089057], [32.925012, -97.088975], [32.924949, -97.088871]], {color: 'red'}).addTo(map);
L.polygon([[32.924853, -97.088953], [32.924790, -97.088848], [32.924699, -97.088925], [32.924824, -97.089135], [32.924916, -97.089058], [32.924853, -97.088953]], {color: 'red'}).addTo(map);
L.polygon([[32.924762, -97.089030], [32.924699, -97.088926], [32.924604, -97.089007], [32.924731, -97.089215], [32.924825, -97.089134], [32.924762, -97.089030]], {color: 'red'}).addTo(map);
L.polygon([[32.924668, -97.089111], [32.924646, -97.089077], [32.924558, -97.089156], [32.924601, -97.089224], [32.924690, -97.089145], [32.924668, -97.089111]], {color: 'red'}).addTo(map);
L.polygon([[32.924580, -97.089190], [32.924558, -97.089156], [32.924470, -97.089235], [32.924513, -97.089303], [32.924602, -97.089224], [32.924580, -97.089190]], {color: 'red'}).addTo(map);
L.polygon([[32.924492, -97.089269], [32.924470, -97.089235], [32.924386, -97.089310], [32.924429, -97.089379], [32.924514, -97.089303], [32.924492, -97.089269]], {color: 'red'}).addTo(map);
L.polygon([[32.924407, -97.089345], [32.924385, -97.089311], [32.924297, -97.089389], [32.924340, -97.089458], [32.924429, -97.089379], [32.924407, -97.089345]], {color: 'red'}).addTo(map);
L.polygon([[32.924318, -97.089424], [32.924297, -97.089389], [32.924212, -97.089463], [32.924255, -97.089532], [32.924339, -97.089459], [32.924318, -97.089424]], {color: 'red'}).addTo(map);
L.polygon([[32.924234, -97.089497], [32.924213, -97.089463], [32.924139, -97.089526], [32.924182, -97.089595], [32.924255, -97.089531], [32.924234, -97.089497]], {color: 'red'}).addTo(map);
L.polygon([[32.924161, -97.089561], [32.924140, -97.089527], [32.924051, -97.089603], [32.924094, -97.089672], [32.924182, -97.089595], [32.924161, -97.089561]], {color: 'red'}).addTo(map);
L.polygon([[32.924073, -97.089638], [32.924052, -97.089603], [32.923935, -97.089704], [32.923978, -97.089773], [32.924094, -97.089673], [32.924073, -97.089638]], {color: 'red'}).addTo(map);
L.polygon([[32.923957, -97.089739], [32.923936, -97.089704], [32.923798, -97.089824], [32.923840, -97.089893], [32.923978, -97.089774], [32.923957, -97.089739]], {color: 'red'}).addTo(map);
L.polygon([[32.923820, -97.089858], [32.923799, -97.089823], [32.923704, -97.089905], [32.923746, -97.089974], [32.923841, -97.089893], [32.923820, -97.089858]], {color: 'red'}).addTo(map);
L.polygon([[32.923725, -97.089940], [32.923704, -97.089906], [32.923612, -97.089985], [32.923655, -97.090054], [32.923746, -97.089974], [32.923725, -97.089940]], {color: 'red'}).addTo(map);
L.polygon([[32.923634, -97.090020], [32.923613, -97.089985], [32.923511, -97.090072], [32.923554, -97.090142], [32.923655, -97.090055], [32.923634, -97.090020]], {color: 'red'}).addTo(map);
L.polygon([[32.923533, -97.090107], [32.923512, -97.090072], [32.923413, -97.090158], [32.923455, -97.090227], [32.923554, -97.090142], [32.923533, -97.090107]], {color: 'red'}).addTo(map);
L.polygon([[32.923434, -97.090193], [32.923413, -97.090159], [32.923312, -97.090247], [32.923355, -97.090316], [32.923455, -97.090227], [32.923434, -97.090193]], {color: 'red'}).addTo(map);
L.polygon([[32.923334, -97.090281], [32.923313, -97.090246], [32.923214, -97.090331], [32.923256, -97.090400], [32.923355, -97.090316], [32.923334, -97.090281]], {color: 'red'}).addTo(map);
L.polygon([[32.923235, -97.090366], [32.923214, -97.090332], [32.923115, -97.090418], [32.923158, -97.090486], [32.923256, -97.090400], [32.923235, -97.090366]], {color: 'red'}).addTo(map);
L.polygon([[32.923137, -97.090452], [32.923116, -97.090417], [32.923019, -97.090500], [32.923061, -97.090570], [32.923158, -97.090487], [32.923137, -97.090452]], {color: 'red'}).addTo(map);
L.polygon([[32.923040, -97.090535], [32.923019, -97.090501], [32.922932, -97.090576], [32.922974, -97.090645], [32.923061, -97.090569], [32.923040, -97.090535]], {color: 'red'}).addTo(map);
L.polygon([[32.922953, -97.090611], [32.922932, -97.090576], [32.922801, -97.090689], [32.922844, -97.090758], [32.922974, -97.090646], [32.922953, -97.090611]], {color: 'red'}).addTo(map);
L.polygon([[32.922823, -97.090724], [32.922802, -97.090689], [32.922709, -97.090769], [32.922751, -97.090838], [32.922844, -97.090759], [32.922823, -97.090724]], {color: 'red'}).addTo(map);
L.polygon([[32.922734, -97.090801], [32.922701, -97.090818], [32.922738, -97.090921], [32.922804, -97.090887], [32.922767, -97.090784], [32.922734, -97.090801]], {color: 'red'}).addTo(map);
L.polygon([[32.922777, -97.090920], [32.922749, -97.090947], [32.922823, -97.091054], [32.922879, -97.091000], [32.922805, -97.090893], [32.922777, -97.090920]], {color: 'red'}).addTo(map);
L.polygon([[32.922834, -97.091003], [32.922806, -97.091030], [32.922864, -97.091117], [32.922921, -97.091064], [32.922862, -97.090976], [32.922834, -97.091003]], {color: 'red'}).addTo(map);
L.polygon([[32.922848, -97.091024], [32.922848, -97.091067], [32.922967, -97.091067], [32.922967, -97.090981], [32.922848, -97.090981], [32.922848, -97.091024]], {color: 'red'}).addTo(map);
L.polygon([[32.922848, -97.091024], [32.922848, -97.091067], [32.922942, -97.091067], [32.922942, -97.090981], [32.922848, -97.090981], [32.922848, -97.091024]], {color: 'red'}).addTo(map);
L.polygon([[32.922848, -97.091024], [32.922848, -97.091067], [32.922967, -97.091067], [32.922967, -97.090981], [32.922848, -97.090981], [32.922848, -97.091024]], {color: 'red'}).addTo(map);
L.polygon([[32.922848, -97.091024], [32.922848, -97.091067], [32.922967, -97.091067], [32.922967, -97.090981], [32.922848, -97.090981], [32.922848, -97.091024]], {color: 'red'}).addTo(map);
            L.marker([32.924726, -97.085924]).addTo(map).bindPopup(`<b>Point 0</b><br>Lat: 32.924726<br>Lon: -97.085924<br>Heading: 290.08<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 17.61 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924726,-97.085924&heading=290.08&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924780, -97.086100]).addTo(map).bindPopup(`<b>Point 1</b><br>Lat: 32.924780<br>Lon: -97.086100<br>Heading: 289.91<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 17.07 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924780,-97.086100&heading=289.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924832, -97.086271]).addTo(map).bindPopup(`<b>Point 2</b><br>Lat: 32.924832<br>Lon: -97.086271<br>Heading: 291.48<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 22.13 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924832,-97.086271&heading=291.48&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924905, -97.086492]).addTo(map).bindPopup(`<b>Point 3</b><br>Lat: 32.924905<br>Lon: -97.086492<br>Heading: 291.55<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 18.67 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924905,-97.086492&heading=291.55&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924963, -97.086667]).addTo(map).bindPopup(`<b>Point 4</b><br>Lat: 32.924963<br>Lon: -97.086667<br>Heading: 295.54<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 17.41 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924963,-97.086667&heading=295.54&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925034, -97.086844]).addTo(map).bindPopup(`<b>Point 5</b><br>Lat: 32.925034<br>Lon: -97.086844<br>Heading: 295.71<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 15.14 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925034,-97.086844&heading=295.71&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925093, -97.086990]).addTo(map).bindPopup(`<b>Point 6</b><br>Lat: 32.925093<br>Lon: -97.086990<br>Heading: 297.51<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 16.24 m<br>turn_direction: through<br>Turn: reverse|left|left;through|none|none<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925093,-97.086990&heading=297.51&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925159, -97.087141]).addTo(map).bindPopup(`<b>Point 7</b><br>Lat: 32.925159<br>Lon: -97.087141<br>Heading: 302.85<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 14.67 m<br>turn_direction: through<br>Turn: reverse|left|left;through|none|none<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925159,-97.087141&heading=302.85&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925230, -97.087272]).addTo(map).bindPopup(`<b>Point 8</b><br>Lat: 32.925230<br>Lon: -97.087272<br>Heading: 302.98<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 12.54 m<br>turn_direction: through<br>Turn: reverse|left|left;through|none|none<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925230,-97.087272&heading=302.98&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925291, -97.087384]).addTo(map).bindPopup(`<b>Point 9</b><br>Lat: 32.925291<br>Lon: -97.087384<br>Heading: 302.52<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 12.64 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925291,-97.087384&heading=302.52&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925352, -97.087498]).addTo(map).bindPopup(`<b>Point 10</b><br>Lat: 32.925352<br>Lon: -97.087498<br>Heading: 293.86<br>Left Side: 20.0 m<br>Right Side: 20.0 m<br>Distance: 17.85 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925352,-97.087498&heading=293.86&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925427, -97.087700]).addTo(map).bindPopup(`<b>Point 11</b><br>Lat: 32.925427<br>Lon: -97.087700<br>Heading: 272.99<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.30 m<br>turn_direction: through<br>Turn: left|left;through|none|none<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925427,-97.087700&heading=272.99&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925432, -97.087814]).addTo(map).bindPopup(`<b>Point 12</b><br>Lat: 32.925432<br>Lon: -97.087814<br>Heading: 273.75<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.65 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925432,-97.087814&heading=273.75&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925438, -97.087923]).addTo(map).bindPopup(`<b>Point 13</b><br>Lat: 32.925438<br>Lon: -97.087923<br>Heading: 274.30<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.36 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925438,-97.087923&heading=274.30&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925445, -97.088034]).addTo(map).bindPopup(`<b>Point 14</b><br>Lat: 32.925445<br>Lon: -97.088034<br>Heading: 355.47<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.19 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925445,-97.088034&heading=355.47&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925657, -97.088054]).addTo(map).bindPopup(`<b>Point 15</b><br>Lat: 32.925657<br>Lon: -97.088054<br>Heading: 260.02<br>Left Side: 16.0 m<br>Right Side: 16.0 m<br>Distance: 12.88 m<br>turn_direction: left<br>Turn: left|left;through|none|none<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925657,-97.088054&heading=260.02&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925622, -97.088291]).addTo(map).bindPopup(`<b>Point 16</b><br>Lat: 32.925622<br>Lon: -97.088291<br>Heading: 215.55<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 15.09 m<br>turn_direction: left<br>Turn: left||<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925622,-97.088291&heading=215.55&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925521, -97.088377]).addTo(map).bindPopup(`<b>Point 17</b><br>Lat: 32.925521<br>Lon: -97.088377<br>Heading: 215.91<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 18.14 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925521,-97.088377&heading=215.91&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925390, -97.088490]).addTo(map).bindPopup(`<b>Point 18</b><br>Lat: 32.925390<br>Lon: -97.088490<br>Heading: 215.96<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.06 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925390,-97.088490&heading=215.96&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925309, -97.088560]).addTo(map).bindPopup(`<b>Point 19</b><br>Lat: 32.925309<br>Lon: -97.088560<br>Heading: 215.97<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.20 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925309,-97.088560&heading=215.97&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925213, -97.088643]).addTo(map).bindPopup(`<b>Point 20</b><br>Lat: 32.925213<br>Lon: -97.088643<br>Heading: 215.68<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.39 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925213,-97.088643&heading=215.68&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925123, -97.088720]).addTo(map).bindPopup(`<b>Point 21</b><br>Lat: 32.925123<br>Lon: -97.088720<br>Heading: 215.88<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.90 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925123,-97.088720&heading=215.88&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.925029, -97.088801]).addTo(map).bindPopup(`<b>Point 22</b><br>Lat: 32.925029<br>Lon: -97.088801<br>Heading: 216.30<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 11.04 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.925029,-97.088801&heading=216.30&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924949, -97.088871]).addTo(map).bindPopup(`<b>Point 23</b><br>Lat: 32.924949<br>Lon: -97.088871<br>Heading: 215.64<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 13.13 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924949,-97.088871&heading=215.64&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924853, -97.088953]).addTo(map).bindPopup(`<b>Point 24</b><br>Lat: 32.924853<br>Lon: -97.088953<br>Heading: 215.38<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.45 m<br>turn_direction: through<br>Turn: left|none|none<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924853,-97.088953&heading=215.38&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924762, -97.089030]).addTo(map).bindPopup(`<b>Point 25</b><br>Lat: 32.924762<br>Lon: -97.089030<br>Heading: 215.88<br>Left Side: 12.0 m<br>Right Side: 12.0 m<br>Distance: 12.89 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924762,-97.089030&heading=215.88&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924668, -97.089111]).addTo(map).bindPopup(`<b>Point 26</b><br>Lat: 32.924668<br>Lon: -97.089111<br>Heading: 217.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.27 m<br>turn_direction: through<br>Turn: left|none|none<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924668,-97.089111&heading=217.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924580, -97.089190]).addTo(map).bindPopup(`<b>Point 27</b><br>Lat: 32.924580<br>Lon: -97.089190<br>Heading: 217.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.29 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924580,-97.089190&heading=217.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924492, -97.089269]).addTo(map).bindPopup(`<b>Point 28</b><br>Lat: 32.924492<br>Lon: -97.089269<br>Heading: 216.89<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.77 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924492,-97.089269&heading=216.89&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924407, -97.089345]).addTo(map).bindPopup(`<b>Point 29</b><br>Lat: 32.924407<br>Lon: -97.089345<br>Heading: 216.69<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.30 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924407,-97.089345&heading=216.69&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924318, -97.089424]).addTo(map).bindPopup(`<b>Point 30</b><br>Lat: 32.924318<br>Lon: -97.089424<br>Heading: 216.11<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.60 m<br>turn_direction: None<br>Turn: right<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924318,-97.089424&heading=216.11&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924234, -97.089497]).addTo(map).bindPopup(`<b>Point 31</b><br>Lat: 32.924234<br>Lon: -97.089497<br>Heading: 216.35<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.09 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924234,-97.089497&heading=216.35&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924161, -97.089561]).addTo(map).bindPopup(`<b>Point 32</b><br>Lat: 32.924161<br>Lon: -97.089561<br>Heading: 216.30<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.15 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924161,-97.089561&heading=216.30&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.924073, -97.089638]).addTo(map).bindPopup(`<b>Point 33</b><br>Lat: 32.924073<br>Lon: -97.089638<br>Heading: 216.16<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 15.97 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.924073,-97.089638&heading=216.16&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.923957, -97.089739]).addTo(map).bindPopup(`<b>Point 34</b><br>Lat: 32.923957<br>Lon: -97.089739<br>Heading: 216.10<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 18.95 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.923957,-97.089739&heading=216.10&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.923820, -97.089858]).addTo(map).bindPopup(`<b>Point 35</b><br>Lat: 32.923820<br>Lon: -97.089858<br>Heading: 215.92<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.99 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.923820,-97.089858&heading=215.92&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.923725, -97.089940]).addTo(map).bindPopup(`<b>Point 36</b><br>Lat: 32.923725<br>Lon: -97.089940<br>Heading: 216.42<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.57 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.923725,-97.089940&heading=216.42&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.923634, -97.090020]).addTo(map).bindPopup(`<b>Point 37</b><br>Lat: 32.923634<br>Lon: -97.090020<br>Heading: 215.87<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.90 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.923634,-97.090020&heading=215.87&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.923533, -97.090107]).addTo(map).bindPopup(`<b>Point 38</b><br>Lat: 32.923533<br>Lon: -97.090107<br>Heading: 216.10<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.57 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.923533,-97.090107&heading=216.10&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.923434, -97.090193]).addTo(map).bindPopup(`<b>Point 39</b><br>Lat: 32.923434<br>Lon: -97.090193<br>Heading: 216.45<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.88 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.923434,-97.090193&heading=216.45&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.923334, -97.090281]).addTo(map).bindPopup(`<b>Point 40</b><br>Lat: 32.923334<br>Lon: -97.090281<br>Heading: 215.78<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.52 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.923334,-97.090281&heading=215.78&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.923235, -97.090366]).addTo(map).bindPopup(`<b>Point 41</b><br>Lat: 32.923235<br>Lon: -97.090366<br>Heading: 216.38<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.57 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.923235,-97.090366&heading=216.38&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.923137, -97.090452]).addTo(map).bindPopup(`<b>Point 42</b><br>Lat: 32.923137<br>Lon: -97.090452<br>Heading: 215.69<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.29 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.923137,-97.090452&heading=215.69&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.923040, -97.090535]).addTo(map).bindPopup(`<b>Point 43</b><br>Lat: 32.923040<br>Lon: -97.090535<br>Heading: 216.25<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 11.97 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.923040,-97.090535&heading=216.25&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.922953, -97.090611]).addTo(map).bindPopup(`<b>Point 44</b><br>Lat: 32.922953<br>Lon: -97.090611<br>Heading: 216.12<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 17.92 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.922953,-97.090611&heading=216.12&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.922823, -97.090724]).addTo(map).bindPopup(`<b>Point 45</b><br>Lat: 32.922823<br>Lon: -97.090724<br>Heading: 215.99<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.71 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.922823,-97.090724&heading=215.99&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.922734, -97.090801]).addTo(map).bindPopup(`<b>Point 46</b><br>Lat: 32.922734<br>Lon: -97.090801<br>Heading: 293.29<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.45 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.922734,-97.090801&heading=293.29&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.922777, -97.090920]).addTo(map).bindPopup(`<b>Point 47</b><br>Lat: 32.922777<br>Lon: -97.090920<br>Heading: 309.29<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 12.94 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.922777,-97.090920&heading=309.29&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.922834, -97.091003]).addTo(map).bindPopup(`<b>Point 48</b><br>Lat: 32.922834<br>Lon: -97.091003<br>Heading: 308.46<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.42 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.922834,-97.091003&heading=308.46&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.922848, -97.091024]).addTo(map).bindPopup(`<b>Point 49</b><br>Lat: 32.922848<br>Lon: -97.091024<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.20 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.922848,-97.091024&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.922848, -97.091024]).addTo(map).bindPopup(`<b>Point 50</b><br>Lat: 32.922848<br>Lon: -97.091024<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 10.42 m<br>turn_direction: None<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.922848,-97.091024&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.922848, -97.091024]).addTo(map).bindPopup(`<b>Point 51</b><br>Lat: 32.922848<br>Lon: -97.091024<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.20 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.922848,-97.091024&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
L.marker([32.922848, -97.091024]).addTo(map).bindPopup(`<b>Point 52</b><br>Lat: 32.922848<br>Lon: -97.091024<br>Heading: 0.00<br>Left Side: 4 m<br>Right Side: 4 m<br>Distance: 13.20 m<br>turn_direction: nan<br>Turn: nan<br><a href='https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=32.922848,-97.091024&heading=0.00&pitch=0&fov=80' target='_blank'>Street View</a>`);
        </script>
        </body>
        </html>
        