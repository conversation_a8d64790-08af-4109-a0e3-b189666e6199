
<!DOCTYPE html>
<html>
<head>
    <title>GPS Rectangles Map</title>
    <meta charset="utf-8" />
    <style> #map { height: 100vh; width: 100%; } </style>
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
</head>
<body>
<div id="map"></div>
<script>
    var map = L.map('map').setView([38.692098, -77.215172], 18);
    <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 22,
        attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);

    L.polygon([[38.692098, -77.215172], [38.692123, -77.215160], [38.692093, -77.215052], [38.692042, -77.215075], [38.692073, -77.215184], [38.692098, -77.215172]], {color: 'red'}).addTo(map);
L.polygon([[38.692937, -77.218198], [38.692912, -77.218210], [38.692942, -77.218318], [38.692993, -77.218295], [38.692962, -77.218186], [38.692937, -77.218198]], {color: 'red'}).addTo(map);
L.polygon([[38.782346, -77.182232], [38.782354, -77.182265], [38.782440, -77.182231], [38.782424, -77.182165], [38.782338, -77.182199], [38.782346, -77.182232]], {color: 'red'}).addTo(map);
L.polygon([[38.778412, -77.176615], [38.778432, -77.176592], [38.778372, -77.176506], [38.778332, -77.176552], [38.778392, -77.176638], [38.778412, -77.176615]], {color: 'red'}).addTo(map);
L.polygon([[38.830183, -77.112866], [38.830202, -77.112891], [38.830267, -77.112811], [38.830229, -77.112761], [38.830164, -77.112841], [38.830183, -77.112866]], {color: 'red'}).addTo(map);
L.polygon([[38.887443, -77.031882], [38.887463, -77.031905], [38.887524, -77.031820], [38.887484, -77.031773], [38.887423, -77.031859], [38.887443, -77.031882]], {color: 'red'}).addTo(map);
L.polygon([[38.887724, -77.031964], [38.887718, -77.031998], [38.887806, -77.032023], [38.887818, -77.031956], [38.887730, -77.031930], [38.887724, -77.031964]], {color: 'red'}).addTo(map);
L.polygon([[38.888509, -77.031961], [38.888509, -77.031996], [38.888599, -77.031995], [38.888599, -77.031926], [38.888509, -77.031926], [38.888509, -77.031961]], {color: 'red'}).addTo(map);
L.polygon([[38.888711, -77.031962], [38.888711, -77.031997], [38.888801, -77.031997], [38.888801, -77.031928], [38.888711, -77.031927], [38.888711, -77.031962]], {color: 'red'}).addTo(map);
L.polygon([[38.890332, -77.031955], [38.890332, -77.031990], [38.890422, -77.031989], [38.890422, -77.031920], [38.890332, -77.031920], [38.890332, -77.031955]], {color: 'red'}).addTo(map);
L.polygon([[38.890511, -77.031953], [38.890511, -77.031988], [38.890601, -77.031987], [38.890601, -77.031917], [38.890511, -77.031918], [38.890511, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.891956, -77.031958], [38.891956, -77.031993], [38.892046, -77.031993], [38.892046, -77.031924], [38.891956, -77.031923], [38.891956, -77.031958]], {color: 'red'}).addTo(map);
L.polygon([[38.892239, -77.031961], [38.892239, -77.031996], [38.892329, -77.031997], [38.892329, -77.031927], [38.892239, -77.031926], [38.892239, -77.031961]], {color: 'red'}).addTo(map);
L.polygon([[38.893299, -77.031959], [38.893299, -77.031994], [38.893389, -77.031993], [38.893389, -77.031924], [38.893299, -77.031924], [38.893299, -77.031959]], {color: 'red'}).addTo(map);
L.polygon([[38.894587, -77.031957], [38.894587, -77.031992], [38.894677, -77.031991], [38.894677, -77.031922], [38.894587, -77.031922], [38.894587, -77.031957]], {color: 'red'}).addTo(map);
L.polygon([[38.903818, -77.031957], [38.903818, -77.031992], [38.903908, -77.031992], [38.903908, -77.031922], [38.903818, -77.031922], [38.903818, -77.031957]], {color: 'red'}).addTo(map);
L.polygon([[38.904851, -77.031909], [38.904852, -77.031944], [38.904942, -77.031939], [38.904940, -77.031870], [38.904850, -77.031874], [38.904851, -77.031909]], {color: 'red'}).addTo(map);
L.polygon([[38.905243, -77.031860], [38.905246, -77.031894], [38.905335, -77.031883], [38.905330, -77.031814], [38.905240, -77.031826], [38.905243, -77.031860]], {color: 'red'}).addTo(map);
L.polygon([[38.905593, -77.031522], [38.905609, -77.031550], [38.905681, -77.031480], [38.905649, -77.031425], [38.905577, -77.031494], [38.905593, -77.031522]], {color: 'red'}).addTo(map);
L.polygon([[38.906229, -77.031819], [38.906220, -77.031852], [38.906304, -77.031891], [38.906323, -77.031826], [38.906238, -77.031786], [38.906229, -77.031819]], {color: 'red'}).addTo(map);
L.polygon([[38.907141, -77.031955], [38.907138, -77.031989], [38.907227, -77.032003], [38.907234, -77.031934], [38.907144, -77.031921], [38.907141, -77.031955]], {color: 'red'}).addTo(map);
L.polygon([[38.907329, -77.031954], [38.907329, -77.031989], [38.907419, -77.031988], [38.907419, -77.031919], [38.907329, -77.031919], [38.907329, -77.031954]], {color: 'red'}).addTo(map);
L.polygon([[38.908647, -77.031952], [38.908647, -77.031987], [38.908737, -77.031986], [38.908737, -77.031917], [38.908647, -77.031917], [38.908647, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.908670, -77.031952], [38.908670, -77.031987], [38.908760, -77.031987], [38.908760, -77.031917], [38.908670, -77.031917], [38.908670, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.908701, -77.031952], [38.908701, -77.031987], [38.908791, -77.031987], [38.908791, -77.031917], [38.908701, -77.031917], [38.908701, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.908742, -77.031951], [38.908743, -77.031986], [38.908833, -77.031983], [38.908832, -77.031914], [38.908741, -77.031916], [38.908742, -77.031951]], {color: 'red'}).addTo(map);
L.polygon([[38.908913, -77.031949], [38.908913, -77.031984], [38.909003, -77.031983], [38.909003, -77.031913], [38.908913, -77.031914], [38.908913, -77.031949]], {color: 'red'}).addTo(map);
L.polygon([[38.908976, -77.031949], [38.908976, -77.031984], [38.909066, -77.031984], [38.909066, -77.031914], [38.908976, -77.031914], [38.908976, -77.031949]], {color: 'red'}).addTo(map);
L.polygon([[38.909541, -77.031952], [38.909541, -77.031987], [38.909631, -77.031987], [38.909631, -77.031918], [38.909541, -77.031917], [38.909541, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.909786, -77.031952], [38.909786, -77.031987], [38.909876, -77.031987], [38.909876, -77.031917], [38.909786, -77.031917], [38.909786, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.911055, -77.031952], [38.911055, -77.031987], [38.911145, -77.031987], [38.911145, -77.031917], [38.911055, -77.031917], [38.911055, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.911196, -77.031953], [38.911196, -77.031988], [38.911286, -77.031988], [38.911286, -77.031919], [38.911196, -77.031918], [38.911196, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.911808, -77.031953], [38.911808, -77.031988], [38.911898, -77.031988], [38.911898, -77.031918], [38.911808, -77.031918], [38.911808, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.911979, -77.031955], [38.911979, -77.031990], [38.912069, -77.031991], [38.912069, -77.031921], [38.911979, -77.031920], [38.911979, -77.031955]], {color: 'red'}).addTo(map);
L.polygon([[38.912471, -77.031952], [38.912471, -77.031987], [38.912561, -77.031986], [38.912561, -77.031917], [38.912471, -77.031917], [38.912471, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.912716, -77.031955], [38.912716, -77.031990], [38.912806, -77.031991], [38.912806, -77.031922], [38.912716, -77.031920], [38.912716, -77.031955]], {color: 'red'}).addTo(map);
L.polygon([[38.913452, -77.031953], [38.913452, -77.031988], [38.913542, -77.031987], [38.913542, -77.031918], [38.913452, -77.031918], [38.913452, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.914027, -77.031954], [38.914027, -77.031989], [38.914117, -77.031989], [38.914117, -77.031920], [38.914027, -77.031919], [38.914027, -77.031954]], {color: 'red'}).addTo(map);
L.polygon([[38.914158, -77.031956], [38.914158, -77.031991], [38.914248, -77.031992], [38.914248, -77.031923], [38.914158, -77.031921], [38.914158, -77.031956]], {color: 'red'}).addTo(map);
L.polygon([[38.914753, -77.031952], [38.914753, -77.031987], [38.914843, -77.031986], [38.914843, -77.031917], [38.914753, -77.031917], [38.914753, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.915469, -77.031953], [38.915469, -77.031988], [38.915559, -77.031988], [38.915559, -77.031919], [38.915469, -77.031918], [38.915469, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.915666, -77.031955], [38.915666, -77.031990], [38.915756, -77.031991], [38.915756, -77.031921], [38.915666, -77.031920], [38.915666, -77.031955]], {color: 'red'}).addTo(map);
L.polygon([[38.916255, -77.031952], [38.916255, -77.031987], [38.916345, -77.031986], [38.916345, -77.031917], [38.916255, -77.031917], [38.916255, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.916895, -77.031953], [38.916895, -77.031988], [38.916985, -77.031988], [38.916985, -77.031919], [38.916895, -77.031918], [38.916895, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.917177, -77.031953], [38.917177, -77.031988], [38.917267, -77.031988], [38.917267, -77.031918], [38.917177, -77.031918], [38.917177, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.918028, -77.031954], [38.918028, -77.031989], [38.918118, -77.031989], [38.918118, -77.031920], [38.918028, -77.031919], [38.918028, -77.031954]], {color: 'red'}).addTo(map);
L.polygon([[38.918187, -77.031953], [38.918187, -77.031988], [38.918277, -77.031987], [38.918277, -77.031918], [38.918187, -77.031918], [38.918187, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.895320, -77.031953], [38.895320, -77.031918], [38.895230, -77.031918], [38.895230, -77.031988], [38.895320, -77.031988], [38.895320, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.895649, -77.031952], [38.895649, -77.031987], [38.895739, -77.031986], [38.895739, -77.031917], [38.895649, -77.031917], [38.895649, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.895985, -77.031955], [38.895985, -77.031990], [38.896075, -77.031990], [38.896075, -77.031921], [38.895985, -77.031920], [38.895985, -77.031955]], {color: 'red'}).addTo(map);
L.polygon([[38.896233, -77.031956], [38.896233, -77.031991], [38.896323, -77.031991], [38.896323, -77.031922], [38.896233, -77.031921], [38.896233, -77.031956]], {color: 'red'}).addTo(map);
L.polygon([[38.897238, -77.031952], [38.897238, -77.031987], [38.897328, -77.031986], [38.897328, -77.031917], [38.897238, -77.031917], [38.897238, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.897526, -77.031955], [38.897526, -77.031990], [38.897616, -77.031991], [38.897616, -77.031921], [38.897526, -77.031920], [38.897526, -77.031955]], {color: 'red'}).addTo(map);
L.polygon([[38.898229, -77.031955], [38.898229, -77.031990], [38.898319, -77.031990], [38.898319, -77.031920], [38.898229, -77.031920], [38.898229, -77.031955]], {color: 'red'}).addTo(map);
L.polygon([[38.898453, -77.031955], [38.898453, -77.031990], [38.898543, -77.031990], [38.898543, -77.031920], [38.898453, -77.031920], [38.898453, -77.031955]], {color: 'red'}).addTo(map);
L.polygon([[38.899291, -77.031950], [38.899291, -77.031985], [38.899381, -77.031984], [38.899381, -77.031915], [38.899291, -77.031915], [38.899291, -77.031950]], {color: 'red'}).addTo(map);
L.polygon([[38.899572, -77.031952], [38.899572, -77.031987], [38.899662, -77.031987], [38.899662, -77.031918], [38.899572, -77.031917], [38.899572, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.900115, -77.031956], [38.900115, -77.031991], [38.900205, -77.031991], [38.900205, -77.031922], [38.900115, -77.031921], [38.900115, -77.031956]], {color: 'red'}).addTo(map);
L.polygon([[38.900343, -77.031955], [38.900343, -77.031990], [38.900433, -77.031989], [38.900433, -77.031920], [38.900343, -77.031920], [38.900343, -77.031955]], {color: 'red'}).addTo(map);
L.polygon([[38.901266, -77.031951], [38.901266, -77.031986], [38.901356, -77.031985], [38.901356, -77.031916], [38.901266, -77.031916], [38.901266, -77.031951]], {color: 'red'}).addTo(map);
L.polygon([[38.901421, -77.031953], [38.901421, -77.031988], [38.901511, -77.031989], [38.901511, -77.031920], [38.901421, -77.031918], [38.901421, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.902714, -77.031953], [38.902714, -77.031988], [38.902804, -77.031988], [38.902804, -77.031918], [38.902714, -77.031918], [38.902714, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.903623, -77.031954], [38.903623, -77.031989], [38.903713, -77.031989], [38.903713, -77.031920], [38.903623, -77.031919], [38.903623, -77.031954]], {color: 'red'}).addTo(map);
L.polygon([[38.919112, -77.031953], [38.919112, -77.031988], [38.919202, -77.031988], [38.919202, -77.031918], [38.919112, -77.031918], [38.919112, -77.031953]], {color: 'red'}).addTo(map);
L.polygon([[38.919194, -77.031952], [38.919194, -77.031987], [38.919284, -77.031985], [38.919284, -77.031916], [38.919194, -77.031917], [38.919194, -77.031952]], {color: 'red'}).addTo(map);
L.polygon([[38.919199, -77.031855], [38.919226, -77.031857], [38.919232, -77.031742], [38.919178, -77.031738], [38.919172, -77.031853], [38.919199, -77.031855]], {color: 'red'}).addTo(map);
L.polygon([[38.919199, -77.031817], [38.919226, -77.031817], [38.919226, -77.031702], [38.919172, -77.031702], [38.919172, -77.031817], [38.919199, -77.031817]], {color: 'red'}).addTo(map);
L.polygon([[38.778412, -77.176615], [38.778395, -77.176588], [38.778325, -77.176660], [38.778359, -77.176714], [38.778429, -77.176642], [38.778412, -77.176615]], {color: 'red'}).addTo(map);
L.polygon([[38.830183, -77.112866], [38.830202, -77.112891], [38.830267, -77.112811], [38.830229, -77.112761], [38.830164, -77.112841], [38.830183, -77.112866]], {color: 'red'}).addTo(map);
    L.marker([38.692098, -77.215172]).addTo(map).bindPopup('Point 0');
L.marker([38.692937, -77.218198]).addTo(map).bindPopup('Point 1');
L.marker([38.782346, -77.182232]).addTo(map).bindPopup('Point 2');
L.marker([38.778412, -77.176615]).addTo(map).bindPopup('Point 3');
L.marker([38.830183, -77.112866]).addTo(map).bindPopup('Point 4');
L.marker([38.887443, -77.031882]).addTo(map).bindPopup('Point 5');
L.marker([38.887724, -77.031964]).addTo(map).bindPopup('Point 6');
L.marker([38.888509, -77.031961]).addTo(map).bindPopup('Point 7');
L.marker([38.888711, -77.031962]).addTo(map).bindPopup('Point 8');
L.marker([38.890332, -77.031955]).addTo(map).bindPopup('Point 9');
L.marker([38.890511, -77.031953]).addTo(map).bindPopup('Point 10');
L.marker([38.891956, -77.031958]).addTo(map).bindPopup('Point 11');
L.marker([38.892239, -77.031961]).addTo(map).bindPopup('Point 12');
L.marker([38.893299, -77.031959]).addTo(map).bindPopup('Point 13');
L.marker([38.894587, -77.031957]).addTo(map).bindPopup('Point 14');
L.marker([38.903818, -77.031957]).addTo(map).bindPopup('Point 15');
L.marker([38.904851, -77.031909]).addTo(map).bindPopup('Point 16');
L.marker([38.905243, -77.031860]).addTo(map).bindPopup('Point 17');
L.marker([38.905593, -77.031522]).addTo(map).bindPopup('Point 18');
L.marker([38.906229, -77.031819]).addTo(map).bindPopup('Point 19');
L.marker([38.907141, -77.031955]).addTo(map).bindPopup('Point 20');
L.marker([38.907329, -77.031954]).addTo(map).bindPopup('Point 21');
L.marker([38.908647, -77.031952]).addTo(map).bindPopup('Point 22');
L.marker([38.908670, -77.031952]).addTo(map).bindPopup('Point 23');
L.marker([38.908701, -77.031952]).addTo(map).bindPopup('Point 24');
L.marker([38.908742, -77.031951]).addTo(map).bindPopup('Point 25');
L.marker([38.908913, -77.031949]).addTo(map).bindPopup('Point 26');
L.marker([38.908976, -77.031949]).addTo(map).bindPopup('Point 27');
L.marker([38.909541, -77.031952]).addTo(map).bindPopup('Point 28');
L.marker([38.909786, -77.031952]).addTo(map).bindPopup('Point 29');
L.marker([38.911055, -77.031952]).addTo(map).bindPopup('Point 30');
L.marker([38.911196, -77.031953]).addTo(map).bindPopup('Point 31');
L.marker([38.911808, -77.031953]).addTo(map).bindPopup('Point 32');
L.marker([38.911979, -77.031955]).addTo(map).bindPopup('Point 33');
L.marker([38.912471, -77.031952]).addTo(map).bindPopup('Point 34');
L.marker([38.912716, -77.031955]).addTo(map).bindPopup('Point 35');
L.marker([38.913452, -77.031953]).addTo(map).bindPopup('Point 36');
L.marker([38.914027, -77.031954]).addTo(map).bindPopup('Point 37');
L.marker([38.914158, -77.031956]).addTo(map).bindPopup('Point 38');
L.marker([38.914753, -77.031952]).addTo(map).bindPopup('Point 39');
L.marker([38.915469, -77.031953]).addTo(map).bindPopup('Point 40');
L.marker([38.915666, -77.031955]).addTo(map).bindPopup('Point 41');
L.marker([38.916255, -77.031952]).addTo(map).bindPopup('Point 42');
L.marker([38.916895, -77.031953]).addTo(map).bindPopup('Point 43');
L.marker([38.917177, -77.031953]).addTo(map).bindPopup('Point 44');
L.marker([38.918028, -77.031954]).addTo(map).bindPopup('Point 45');
L.marker([38.918187, -77.031953]).addTo(map).bindPopup('Point 46');
L.marker([38.895320, -77.031953]).addTo(map).bindPopup('Point 47');
L.marker([38.895649, -77.031952]).addTo(map).bindPopup('Point 48');
L.marker([38.895985, -77.031955]).addTo(map).bindPopup('Point 49');
L.marker([38.896233, -77.031956]).addTo(map).bindPopup('Point 50');
L.marker([38.897238, -77.031952]).addTo(map).bindPopup('Point 51');
L.marker([38.897526, -77.031955]).addTo(map).bindPopup('Point 52');
L.marker([38.898229, -77.031955]).addTo(map).bindPopup('Point 53');
L.marker([38.898453, -77.031955]).addTo(map).bindPopup('Point 54');
L.marker([38.899291, -77.031950]).addTo(map).bindPopup('Point 55');
L.marker([38.899572, -77.031952]).addTo(map).bindPopup('Point 56');
L.marker([38.900115, -77.031956]).addTo(map).bindPopup('Point 57');
L.marker([38.900343, -77.031955]).addTo(map).bindPopup('Point 58');
L.marker([38.901266, -77.031951]).addTo(map).bindPopup('Point 59');
L.marker([38.901421, -77.031953]).addTo(map).bindPopup('Point 60');
L.marker([38.902714, -77.031953]).addTo(map).bindPopup('Point 61');
L.marker([38.903623, -77.031954]).addTo(map).bindPopup('Point 62');
L.marker([38.919112, -77.031953]).addTo(map).bindPopup('Point 63');
L.marker([38.919194, -77.031952]).addTo(map).bindPopup('Point 64');
L.marker([38.919199, -77.031855]).addTo(map).bindPopup('Point 65');
L.marker([38.919199, -77.031817]).addTo(map).bindPopup('Point 66');
L.marker([38.778412, -77.176615]).addTo(map).bindPopup('Point 67');
L.marker([38.830183, -77.112866]).addTo(map).bindPopup('Point 68');

    let bounds = L.latLngBounds([]);
    map.eachLayer(function (layer) {
        if (layer instanceof L.Polygon) {
            bounds.extend(layer.getBounds());
        }
    });
    if (bounds.isValid()) {
        map.fitBounds(bounds);
    }
</script>
</body>
</html>
