# -*- coding: utf-8 -*-
"""
Created on Mon Oct  24 07:33:04 2023


@author: <PERSON><PERSON>
"""
import inspect
import logging
import sys

sys.path.insert(0, r"/local/mnt/workspace/sagnur/python_projects/disha/api")
# import os
# import sys
import time
from collections import Counter

import numpy as np
import pandas as pd
import requests
from enrichment.compass.map_objects import MapObjects
from enrichment.general.utils import create_mapped_id, get_ego_direction, take_closest_dist
from geopy import Point
from geopy.distance import distance, geodesic
import math

log = logging.getLogger("CustomMapObjects")
handler = logging.StreamHandler()
handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)
log.addHandler(handler)

# %%


class CustomMapObjects(MapObjects):
    def __init__(
        self,
        overpass_server=None,
        radius=5,
        max_retry_count=10,
        retry_timeout=1,
        auth=None,
        min_distance=10,
    ):
        super().__init__(
            overpass_server=overpass_server,
            max_retry_count=max_retry_count,
            retry_timeout=retry_timeout,
        )
        self.auth = auth
        self.radius = radius
        self.min_distance = (min_distance,)

    def downsampleData(self, df):
        """Downsample GPS points to maintain approximately `min_distance` meters between points."""
        df["datetime"] = pd.to_datetime(df["timestamp"], unit="ns")
        df = df.sort_values(by="datetime")
        gpsdistance = df["gpsdistance"].values
        cumsum_distance = np.cumsum(gpsdistance)

        selected_indices = [0]  # Always keep the first point
        last_selected_cumsum = 0

        for i in range(1, len(cumsum_distance)):
            if cumsum_distance[i] - last_selected_cumsum >= self.min_distance:
                selected_indices.append(i)
                last_selected_cumsum = cumsum_distance[i]

        selected_indices = np.array(selected_indices)

        # 1. Create a boolean mask
        is_selected = np.zeros(len(df), dtype=bool)
        is_selected[selected_indices] = True

        # 2. Calculate cumulative_distance at selected points using np.diff
        cumulative_distance = np.zeros(len(df))
        cumulative_distances_selected = np.diff(
            np.concatenate(([0], cumsum_distance[selected_indices]))
        )
        cumulative_distance[selected_indices] = cumulative_distances_selected

        # Assign to dataframe
        df["is_selected_point"] = is_selected
        df["cumulative_distance"] = cumulative_distance

        downsampled_df = df[df["is_selected_point"]].reset_index(drop=True)

        # df.to_csv(
        # "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/turn-86d8f771_downsampled_full.csv",
        # index=False,
        # )

        return downsampled_df

    def get_bearing_angle(self, lat1, lon1, lat2, lon2):
        """
        Calculate the initial compass bearing in degrees from point A to B.
        """
        lat1, lat2 = np.radians([lat1, lat2])
        delta_lon = np.radians(lon2 - lon1)

        x = np.sin(delta_lon) * np.cos(lat2)
        y = np.cos(lat1) * np.sin(lat2) - np.sin(lat1) * np.cos(lat2) * np.cos(delta_lon)

        bearing = np.degrees(np.arctan2(x, y))
        return (bearing + 360) % 360

    def create_polygon(self, i):
        """
        Builds an Overpass QL query with polygon filters aligned along vehicle direction,
        using lat/lon from self.data_c DataFrame.

        Rectangle: width based on lane count (left/right), forward length based on distance between two gps points.
        """
        no_of_lanes = self.data_c.loc[i, "lane"]
        left_side = right_side = 4 if pd.isna(no_of_lanes) or no_of_lanes == 1 else no_of_lanes * 4

        if len(self.data_c) == 1:
            # if only one point is available, use the same point for heading
            lat_ref, lon_ref = self.data_c.loc[i, ["latitude", "longitude"]]
        elif i == (len(self.data_c) - 1):
            # Use previous point for heading if at the last index
            lat_ref, lon_ref = self.data_c.loc[i - 1, ["latitude", "longitude"]]
        else:
            # Use next point to determine heading if not at the last index
            lat_ref, lon_ref = self.data_c.loc[i + 1, ["latitude", "longitude"]]

        lat_curr, lon_curr = self.data_c.loc[i, ["latitude", "longitude"]]
        heading = self.get_bearing_angle(lat_curr, lon_curr, lat_ref, lon_ref)

        left_bearing = (heading - 90) % 360
        right_bearing = (heading + 90) % 360

        point_a = Point(lat_curr, lon_curr)
        point_b = distance(meters=left_side).destination(point_a, left_bearing)
        point_c = distance(meters=right_side).destination(point_a, right_bearing)
        # Handle gps_distance based on the index
        if i + 1 < len(self.data_c):
            cumulative_distance = self.data_c.loc[i + 1, "cumulative_distance"]
        else:
            cumulative_distance = self.data_c.loc[i, "cumulative_distance"]
        # Ensure gps_distance is non-negative
        cumulative_distance = max(cumulative_distance, 0)

        point_d = distance(meters=cumulative_distance).destination(point_b, heading)
        point_e = distance(meters=cumulative_distance).destination(point_c, heading)

        # Polygon: A → B → D → E → C → A
        poly_points = [
            (lat_curr, lon_curr),
            (point_b.latitude, point_b.longitude),
            (point_d.latitude, point_d.longitude),
            (point_e.latitude, point_e.longitude),
            (point_c.latitude, point_c.longitude),
            (lat_curr, lon_curr),
        ]
        poly_str = " ".join(f"{lat:.6f} {lon:.6f}" for lat, lon in poly_points)
        return poly_str

    def execute_query(self, query=None, _retry_count=0):
        """Execute a custom overpass query while respecting retry count and sleep time
        Input:
        query: str, --> Query to pass the overpass server
        _retry_count: int --> number of requests need to try
        auth: --> authentication (if requires)

        Output:
        resp: json --> response of the request
        """
        if query is not None:
            if self.auth is not None:
                resp = requests.post(self.overpass_server, data={"data": query}, auth=self.auth)
            else:
                resp = requests.post(self.overpass_server, data={"data": query})
            if resp.status_code == 200:
                # print("code is 200")
                # print("resp:", resp.json()["elements"])
                # return resp.json()["elements"]
                try:
                    return resp.json()["elements"]
                except ValueError:
                    caller = inspect.stack()[1]
                    print(
                        f"Response from  {caller.function} osm is not a valid JSON: Retry count - {_retry_count}"
                    )
            elif resp.status_code == 429:
                if _retry_count < self.max_retry_count:
                    caller = inspect.stack()[1]
                    print(f"code is 429. Retrying {caller.function}: Retry count - {_retry_count}")
                    time.sleep(self.retry_timeout)
                    self.execute_query(query, _retry_count=(_retry_count + 1))
                    _retry_count += 1
                else:
                    return {"msg": "Too many requests", "retry_count": _retry_count}
            elif resp.status_code == 504:
                if _retry_count < self.max_retry_count:
                    caller = inspect.stack()[1]
                    print(f"code is 504. Retrying {caller.function}: Retry count - {_retry_count}")
                    time.sleep(self.retry_timeout)
                    self.execute_query(query, _retry_count=(_retry_count + 1))
                    _retry_count += 1
                else:
                    print(f"reached max retry count limit of {self.max_retry_count}")
                    print("Exiting the program.")
                    sys.exit(1)
                    return {"msg": "Gateway time out", "retry_count": _retry_count}
        else:
            return None

    def extract_way_tags(self, c, chunk_size, object=None, chunk=None):
        """Extract the tags associated with each way
        Input:
        object: list --> List of objects detected
        Output:
        way_object_data: dataframe --> df containing lat, lon, id and tags value
        """
        if object is not None:
            in_object = object
            way_id_ = []
            id_ = []
            tags_ = []
            lat_ = []
            lon_ = []
            direction = []
            nodeinfo = {"lat": [], "lon": [], "id": []}
            for i in range(0, len(in_object)):
                if in_object[i]["type"] == "node":
                    nodeinfo["lat"].append(in_object[i]["lat"])
                    nodeinfo["lon"].append(in_object[i]["lon"])
                    nodeinfo["id"].append(in_object[i]["id"])
            for i in range(0, len(in_object)):
                if in_object[i]["type"] == "way":
                    node_id = []
                    tags = []
                    lat = []
                    lon = []
                    way_id = []
                    for j in range(len(in_object[i]["nodes"])):
                        way_id.append(in_object[i]["id"])
                        id = in_object[i]["nodes"][j]
                        try:
                            if "tags" in in_object[i]:
                                id_index = nodeinfo["id"].index(id)
                                node_id.append(nodeinfo["id"][id_index])
                                lat.append(nodeinfo["lat"][id_index])
                                lon.append(nodeinfo["lon"][id_index])
                                tags.append(in_object[i]["tags"])
                        except ValueError:
                            print(f"The id {id} is not in the list.")
                    dir_list = get_ego_direction(lat, lon, chunk)
                    if "not matched" not in dir_list:
                        direction.extend(dir_list)
                        id_.extend(node_id)
                        lat_.extend(lat)
                        lon_.extend(lon)
                        tags_.extend(tags)
                        way_id_.extend(way_id)

            way_object_data = pd.DataFrame(tags_)
            way_object_data.insert(0, "longitude", lon_)
            way_object_data.insert(0, "latitude", lat_)
            way_object_data.insert(0, "id", id_)
            way_object_data.insert(0, "way_id", way_id_)
            if "direction" not in way_object_data.columns:
                way_object_data.insert(0, "direction", direction)
            return way_object_data
        else:
            return None

    def extract_node_tags(self, object=None):
        """Extract the tags associated with each node
        Input:
        object: list --> List of objects detected
        Output:
        way_object_data: dataframe --> df containing lat, lon, id and tags value
        """
        if object is not None:
            in_object = object
            node_id_ = []
            node_tags_ = []
            node_lat_ = []
            node_lon_ = []
            nodeinfo = {"lat": [], "lon": [], "id": []}
            for i in range(0, len(in_object)):
                if in_object[i]["type"] == "node":
                    nodeinfo["lat"].append(in_object[i]["lat"])
                    nodeinfo["lon"].append(in_object[i]["lon"])
                    nodeinfo["id"].append(in_object[i]["id"])
                    if "tags" in in_object[i]:
                        node_id_.append(in_object[i]["id"])
                        node_lat_.append(in_object[i]["lat"])
                        node_lon_.append(in_object[i]["lon"])
                        node_tags_.append(in_object[i]["tags"])

            node_object_data = pd.DataFrame(node_tags_)
            node_object_data.insert(0, "longitude", node_lon_)
            node_object_data.insert(0, "latitude", node_lat_)
            node_object_data.insert(0, "id", node_id_)
            return node_object_data
        else:
            return None

    def extract_way_node_tags(self, objects=None):
        """Extract the tags associated with each node and way.
        Input:
        objects: list --> List of OSM objects detected
        Output:
        osm_data: dataframe --> DataFrame containing latitude, longitude, id, and tags values
        """
        if objects is not None:
            nodeinfo = {"lat": [], "lon": [], "id": []}
            for obj in objects:
                if obj["type"] == "node":
                    nodeinfo["lat"].append(obj["lat"])
                    nodeinfo["lon"].append(obj["lon"])
                    nodeinfo["id"].append(obj["id"])
            ids = []
            lats = []
            lons = []
            tags_data = []
            for obj in objects:
                if obj["type"] == "node":
                    # Handle nodes directly
                    if "tags" in obj:
                        ids.append(obj["id"])
                        lats.append(obj["lat"])
                        lons.append(obj["lon"])
                        tags_data.append(obj["tags"])
                elif obj["type"] == "way":
                    if "tags" in obj:
                        for node_id in obj["nodes"]:
                            try:
                                index = nodeinfo["id"].index(node_id)
                                ids.append(nodeinfo["id"][index])
                                lats.append(nodeinfo["lat"][index])
                                lons.append(nodeinfo["lon"][index])
                                tags_data.append(obj["tags"])
                            except ValueError:
                                print(f"The id {node_id} is not in the list.")
            # Create a DataFrame from the retrieved data
            osm_data = pd.DataFrame(tags_data)
            osm_data.insert(0, "longitude", lons)
            osm_data.insert(0, "latitude", lats)
            osm_data.insert(0, "id", ids)
            return osm_data
        else:
            return None

    def extract_way_node_relation_tags(self, objects=None):
        """Extract the tags associated with each node, way, and relation.
        Input:
        objects: list --> List of OSM objects detected
        Output:
        osm_data: dataframe --> DataFrame containing latitude, longitude, id, and tags values
        """
        if objects is not None:
            nodeinfo = {"lat": [], "lon": [], "id": [], "tags": {}}
            wayinfo = {"id": [], "tags": []}

            # First pass: collect all nodes and their information
            for obj in objects:
                if obj["type"] == "node":
                    nodeinfo["lat"].append(obj["lat"])
                    nodeinfo["lon"].append(obj["lon"])
                    nodeinfo["id"].append(obj["id"])
                    if "tags" in obj:
                        nodeinfo["tags"][obj["id"]] = obj["tags"]

                elif obj["type"] == "way":
                    wayinfo["id"].append(obj["id"])
                    wayinfo["tags"].append(obj.get("tags", {}))

            # Prepare lists to hold final data
            ids = []
            lats = []
            lons = []
            tags_data = []

            # Second pass: extract info for nodes, ways and relations
            for obj in objects:
                if obj["type"] == "node":
                    # Handle nodes directly
                    ids.append(obj["id"])
                    lats.append(obj["lat"])
                    lons.append(obj["lon"])
                    tags_data.append(obj.get("tags", {}))

                elif obj["type"] == "way":
                    # Handle ways and extract nodes' info
                    for node_id in obj["nodes"]:
                        if node_id in nodeinfo["id"]:
                            index = nodeinfo["id"].index(node_id)
                            ids.append(nodeinfo["id"][index])
                            lats.append(nodeinfo["lat"][index])
                            lons.append(nodeinfo["lon"][index])
                            tags_data.append(obj.get("tags", {}))  # Use way's tags

                elif obj["type"] == "relation":
                    # Handle relations and their member types
                    for member in obj.get("members", []):
                        member_id = member["ref"]
                        member_type = member["type"]

                        if member_type == "node" and member_id in nodeinfo["id"]:
                            index = nodeinfo["id"].index(member_id)
                            ids.append(nodeinfo["id"][index])
                            lats.append(nodeinfo["lat"][index])
                            lons.append(nodeinfo["lon"][index])
                            tags_data.append(obj.get("tags", {}))  # Use relation's tags
                        elif member_type == "way" and member_id in wayinfo["id"]:
                            index = wayinfo["id"].index(member_id)
                            # For ways, we might not have lat/lon data in the relation context
                            # so we only append the tags, and don't append lat/lon values.
                            tags_data.append(wayinfo["tags"][index])

            # Filter out empty tags_data, and ensure all lists are of the same length
            filtered_indexes = [i for i in range(len(tags_data)) if tags_data[i]]
            filtered_ids = [ids[i] for i in filtered_indexes]
            filtered_lats = [lats[i] for i in filtered_indexes]
            filtered_lons = [lons[i] for i in filtered_indexes]
            filtered_tags_data = [tags_data[i] for i in filtered_indexes]
            # Create a DataFrame from the retrieved data
            osm_data = pd.DataFrame(filtered_tags_data)
            osm_data.insert(0, "longitude", filtered_lons)
            osm_data.insert(0, "latitude", filtered_lats)
            osm_data.insert(0, "id", filtered_ids)
            return osm_data
        else:
            return None

    def convert_to_string(
        self,
    ):
        """We can parse string only for the query which should be separated by
        comma, and this function will give that.
        """
        self.re_ = self.down_sample()
        if self.re_.ndim > 1:
            self.re_ = self.re_.flatten()
        self.xystring = ",".join(["%.7f" % num for num in self.re_])
        return self.xystring

    def get_lane_merge_split_overpass(self, ind=None, dataframe=None, previous_non_zero=None):
        """Checks whether lane merge or split is present for given index
        Input:
        ind: int --> the index for which need to check
        dataframe: df --> the dataframe which contains the info of number of forward lanes available
        previous_non_zero: list --> A list that contains the info of number of lane value at previous non-zero value
        Ex. if df['forward_lane'] = [1,0,0,3,3,3,0,0,0,2,2,2,0,0] then
        previous_non_zero = [1,0,0,1,3,3,0,0,0,3,2,2,0,0] so that we can compare df['forward_lane'][3] with
        previous_non_zero[3] value and decide the kind of change in lane
        """

        flag = True
        if ind is None:
            flag = False
        elif dataframe is None:
            flag = False
        elif previous_non_zero is None:
            flag = False

        if flag:
            if previous_non_zero[ind] != None:
                if previous_non_zero[ind] > dataframe["lane_forward"][ind]:
                    if dataframe["roadtype"][ind - 1] == dataframe["roadtype"][ind]:
                        dataframe.loc[ind, "lanemerge"] = True
                        print("lane merge detected")

                elif previous_non_zero[ind] < dataframe["lane_forward"][ind]:
                    if dataframe["roadtype"][ind - 1] == dataframe["roadtype"][ind]:
                        dataframe.loc[ind, "lanesplit"] = True
                        print("lane split detected")
            return dataframe
        else:
            return None

    def built_oqgw_nwr(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            queries.append(
                f"nwr(around:{self.radius},{self.data_c['latitude'][i]},{self.data_c['longitude'][i]})['highway'='give_way'];"
            )
        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )

        return built_query

    def built_oqgw_nwr_poly(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            poly_str = self.create_polygon(i)
            queries.append(f'nwr(poly:"{poly_str}")["highway"="give_way"];')

        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )

        return built_query

    def built_oqnr_nwr(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            queries.append(
                f"nwr(around:{self.radius},{self.data_c['latitude'][i]},{self.data_c['longitude'][i]})['traffic_calming'];"
            )
        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )

        return built_query

    def built_oqnr_nwr_poly(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            poly_str = self.create_polygon(i)
            queries.append(f'nwr(poly:"{poly_str}")["traffic_calming"];')

        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )
        return built_query

    def built_oqvill_nwr(self):
        self.radius = 500
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            queries.append(
                f"nwr(around:{self.radius},{self.data_c['latitude'][i]},{self.data_c['longitude'][i]})['place'='village'];"
            )
        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )

        return built_query

    def built_oqss_nwr(
        self,
    ):
        self.radius = 20
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            queries.append(
                f"nwr(around:{self.radius},{self.data_c['latitude'][i]},{self.data_c['longitude'][i]})['highway'='stop'];"
            )
        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )

        return built_query

    def built_oqss_nwr_poly(
        self,
    ):
        self.radius = 20
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            poly_str = self.create_polygon(i)
            queries.append(f'nwr(poly:"{poly_str}")["highway"="stop"];')

        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )
        return built_query

    def built_oqpadescross_nwr(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            queries.append(
                f"nwr(around:{self.radius},{self.data_c['latitude'][i]},{self.data_c['longitude'][i]})['highway'='crossing'];"
            )
        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )

        return built_query

    def built_oqpadescross_nwr_poly(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            poly_str = self.create_polygon(i)
            queries.append(f'nwr(poly:"{poly_str}")["highway"="crossing"];')

        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )
        return built_query

    def built_oqlsm(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            queries.append(
                f"way(around:{self.radius},{self.data_c['latitude'][i]},{self.data_c['longitude'][i]})[lanes][change:lanes];"
                f"- way(around:{self.radius},{self.data_c['latitude'][i]},{self.data_c['longitude'][i]})[turn:lanes];"
            )
        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )

        return built_query

    def built_oqlsm_nwr(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            queries.append(
                f"nwr(around:{self.radius},{self.data_c['latitude'][i]},{self.data_c['longitude'][i]})['highway']['lanes'];"
            )
        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )

        return built_query

    def down_sample(self):
        """Function to merge data point and provide a string."""
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        self.data_points = self.data_c.shape[0]
        self.re_ = []
        for i in range(0, self.data_points):
            i = i * int(self.data_c.shape[0] / self.data_points)
            tempo = np.array((self.data_c["latitude"][i], self.data_c["longitude"][i]))
            self.re_.append(tempo)
        self.re_ = np.array(self.re_).reshape(-1, 1)

        return self.re_

    def get_give_way(self, data=None, chunk_size=None, query_type=None):
        """To find give way locations"""
        if data is not None:
            chunks_df = self.initial_process(data, chunk_size)
            self.df_nodes = pd.DataFrame()
            for chunk in chunks_df:
                self.create_coord_string(chunk)
                if query_type == "poly":
                    # print("using polygon based query")
                    query = self.built_oqgw_nwr_poly()
                else:
                    # print("using radius based query")
                    query = self.built_oqgw_nwr()
                resp = self.execute_query(query)
                df_n = self.extract_node_tags(resp)
                self.df_nodes = pd.concat([self.df_nodes, df_n], ignore_index=True)
        column_names = ["latitude", "longitude", "giveway"]
        df = self.data.copy()
        df["giveway"] = False
        if len(self.df_nodes) > 0:
            self.get_mapped_id_node()
            if set(["highway"]).issubset(self.df_nodes):
                for i in range(len(self.mapped_id_nodes)):
                    if self.mapped_id_nodes[i] is not None:
                        ind = self.mapped_id_nodes[i]
                        if self.df_nodes["highway"][i] == "give_way":
                            df.loc[ind, "giveway"] = True

        else:
            print("No reponse from server")
        if df["giveway"].any():
            print("Giveway present")
        else:
            print("no give way sign present")
        return df[column_names]

    def get_narrow_road(self, data=None, chunk_size=None, query_type=None):
        """To find give way locations"""
        if data is not None:
            chunks_df = self.initial_process(data, chunk_size)
            self.df_nodes = pd.DataFrame()
            for chunk in chunks_df:
                self.create_coord_string(chunk)
                if query_type == "poly":
                    # print("using polygon based query")
                    query = self.built_oqnr_nwr_poly()
                else:
                    # print("using radius based query")
                    query = self.built_oqnr_nwr()
                resp = self.execute_query(query)
                # df_n = self.extract_node_tags(resp)
                df_n = self.extract_way_node_relation_tags(resp)
                self.df_nodes = pd.concat([self.df_nodes, df_n], ignore_index=True)
        column_names = ["latitude", "longitude", "narrow_road"]
        df = self.data.copy()
        df["narrow_road"] = False
        if len(self.df_nodes) > 0:
            self.get_mapped_id_node()
            if set(["traffic_calming"]).issubset(self.df_nodes):
                for i in range(len(self.mapped_id_nodes)):
                    if self.mapped_id_nodes[i] is not None:
                        ind = self.mapped_id_nodes[i]
                        if (
                            self.df_nodes["traffic_calming"][i] == "chicane"
                            or self.df_nodes["traffic_calming"][i] == "choker"
                        ):
                            df.loc[ind, "narrow_road"] = True
        else:
            print("No reponse from server")
        if df["narrow_road"].any():
            print("narrow road present")
        else:
            print("no narrow road present")
        return df[column_names]

    def get_village(self, data=None, chunk_size=None):
        """To find give way locations"""
        if data is not None:
            chunks_df = self.initial_process(data, chunk_size)
            self.df_nodes = pd.DataFrame()
            for chunk in chunks_df:
                self.create_coord_string(chunk)
                query = self.built_oqvill_nwr()
                resp = self.execute_query(query)
                # df_n = self.extract_village_tags(resp)
                df_n = self.extract_way_node_relation_tags(resp)
                self.df_nodes = pd.concat([self.df_nodes, df_n], ignore_index=True)
        column_names = ["latitude", "longitude", "roadtype", "village"]
        df = self.data.copy()
        df["village"] = False
        if len(self.df_nodes) > 0:
            self.get_mapped_id_node(tag="village")
            if set(["place"]).issubset(self.df_nodes):
                for i in range(len(self.mapped_id_nodes)):
                    if self.mapped_id_nodes[i] is not None:
                        ind = self.mapped_id_nodes[i]
                        if (self.df_nodes["place"][i] == "village") and (
                            df.loc[ind, "roadtype"]
                            not in ["highway", "not_available", "unknown", "nan"]
                        ):
                            # if self.df_nodes["place"][i] == "village":
                            df.loc[ind, "village"] = True
        else:
            print("No reponse from server")
        if df["village"].any():
            print("village road present")
        else:
            print("no village road present")
        return df[column_names]

    def get_ts_padestrian_crossing(self, data=None, chunk_size=None, query_type=None):
        """To find give way locations"""
        if data is not None:
            chunks_df = self.initial_process(data, chunk_size)
            self.df_nodes = pd.DataFrame()
            for chunk in chunks_df:
                self.create_coord_string(chunk)
                if query_type == "poly":
                    # print("using polygon based query")
                    query = self.built_oqpadescross_nwr_poly()
                else:
                    # print("using radius based query")
                    query = self.built_oqpadescross_nwr()
                resp = self.execute_query(query)
                df_n = self.extract_node_tags(resp)
                self.df_nodes = pd.concat([self.df_nodes, df_n], ignore_index=True)
        column_names = ["latitude", "longitude", "padestriancrossing", "trafficlight"]
        df = self.data.copy()
        df["padestriancrossing"] = False
        df["trafficlight"] = False
        if len(self.df_nodes) > 0:
            self.get_mapped_id_node()
            if set(["highway"]).issubset(self.df_nodes):
                for i in range(len(self.mapped_id_nodes)):
                    if self.mapped_id_nodes[i] is not None:
                        ind = self.mapped_id_nodes[i]
                        if self.df_nodes["highway"][i] == "crossing":
                            df.loc[ind, "padestriancrossing"] = True
                        if "crossing" in self.df_nodes.columns:
                            if self.df_nodes["crossing"][i] == "traffic_signals":
                                df.loc[ind, "trafficlight"] = True
                        else:
                            print("Column 'crossing' not found in self.df_nodes")

        else:
            print("No reponse from server")
        if df["padestriancrossing"].any():
            print("padestrian crossing present")
        else:
            print("No pedestrian crossing present")
        if df["trafficlight"].any():
            print("trafficlight present")
        else:
            print("No traffic light present")
        return df[column_names]

    def get_stop_sign(self, data=None, chunk_size=None, query_type=None):
        """To find give way locations"""
        if data is not None:
            chunks_df = self.initial_process(data, chunk_size)
            self.df_nodes = pd.DataFrame()
            for chunk in chunks_df:
                self.create_coord_string(chunk)
                if query_type == "poly":
                    # print("using polygon based query")
                    query = self.built_oqss_nwr_poly()
                else:
                    # print("using radius based query")
                    query = self.built_oqss_nwr()
                resp = self.execute_query(query)
                df_n = self.extract_node_tags(resp)
                self.df_nodes = pd.concat([self.df_nodes, df_n], ignore_index=True)
        column_names = ["latitude", "longitude", "stopsign", "4waystop"]
        # self.df_nodes[['latitude', 'longitude']].to_csv('node_out.csv')
        df = self.data.copy()
        df["stopsign"] = False
        df["4waystop"] = False
        if len(self.df_nodes) > 0:
            self.get_mapped_id_node()
            if set(["highway"]).issubset(self.df_nodes):
                for i in range(len(self.mapped_id_nodes)):
                    if self.mapped_id_nodes[i] is not None:
                        ind = self.mapped_id_nodes[i]
                        if self.df_nodes["highway"][i] == "stop":
                            df.loc[ind, "stopsign"] = True
            if set(["stop"]).issubset(self.df_nodes):
                for i in range(len(self.mapped_id_nodes)):
                    if self.mapped_id_nodes[i] is not None:
                        ind = self.mapped_id_nodes[i]
                        if self.df_nodes["stop"][i] == "all":
                            df.loc[ind, "4waystop"] = True
        else:
            print("No reponse from server")
        if df["stopsign"].any():
            print("stop sign present")
        else:
            print("no stop sign detected")
        if df["4waystop"].any():
            print("4way stop sign present")
        else:
            print("no 4waystop detected")
        return df[column_names]

    def get_ego_direction(self, map_way):
        """Get the direction of ego based on way direction"""
        way_ids = self.df_ways["way_id"].unique()
        ego_direction = []
        way_df_all = pd.DataFrame()
        for id in way_ids:
            indexes = self.df_ways["way_id"].index[self.df_ways["way_id"] == id].tolist()
            way_df = self.df_ways.loc[indexes, ["latitude", "longitude"]]
            way_df_test = self.df_ways.loc[indexes, ["latitude", "longitude"]]
            way_df_test.insert(0, "id", len(way_df_test) * [id])
            df_ind = []
            for ind in indexes:
                df_ind.extend(np.where(np.array(map_way) == ind)[0])
            ego_pos_df = self.data.iloc[df_ind]
            if len(ego_pos_df) > 0:
                ego_direction.extend(get_ego_direction(ego_pos_df, way_df))
                way_df_all = pd.concat([way_df_all, way_df_test], ignore_index=True)

            else:
                ego_direction.extend(len(way_df) * ["not_known"])
        # way_df_all.to_csv(f"way.csv")
        self.df_ways["ego_direction"] = ego_direction

    def get_matched_way(self, df_w, chunk, df_len, map_way):
        map_id = []
        for i in range(len(chunk["latitude"])):
            p = (float(chunk["latitude"].iloc[i]), float(chunk["longitude"].iloc[i]))
            id, cutoff = take_closest_dist(df_w, p)
            if cutoff < 10:
                map_id.append(id)
            else:
                map_id.append(None)
        seen = []
        non_none_indexes = []
        for i in map_id:
            if i is not None and i not in seen:
                non_none_indexes.append(i)
                seen.append(i)
                map_way.append(df_len + seen.index(i))
            elif i in seen:
                map_way.append(df_len + seen.index(i))
            else:
                map_way.append(i)
        if len(non_none_indexes) > 0:
            df = df_w.iloc[non_none_indexes].reset_index(drop=True)
        else:
            df = pd.DataFrame()

        return df, map_way

    def get_mapped_id_node(self, tag=None):
        """map the id from data with map id"""
        self.mapped_id_nodes = create_mapped_id(self.data.copy(), self.df_nodes, False, tag)

    def initial_process(self, data, chunk_size):
        if "lat" in data:
            data.rename(columns={"lat": "latitude", "lon": "longitude"}, inplace=True)
        self.data = data
        self.chunk_size = chunk_size
        num_chunks = int(np.ceil(len(self.data) / self.chunk_size))
        chunks_df = [
            self.data[i * self.chunk_size : (i + 1) * self.chunk_size].reset_index(drop=True)
            for i in range(num_chunks)
        ]

        return chunks_df

    def create_coord_string(self, chunk):
        self.data_c = chunk.copy()
        self.data_points = self.data_c.shape[0]
        self.convert_to_string()

    def get_lanesplit_merge(self, data=None, chunk_size=100):
        """Get the location of lane split or lane merge"""

        if data is not None:
            if "lat" in data:
                data.rename(columns={"lat": "latitude", "lon": "longitude"}, inplace=True)
            req_data = data[["latitude", "longitude"]]
            chunks_df = self.initial_process(req_data, chunk_size)
            self.df_ways = pd.DataFrame()
            map_way = []
            c = 0
            for chunk in chunks_df:
                c = c + 1
                self.create_coord_string(chunk)
                query = self.built_oqlsm_nwr()
                resp = self.execute_query(query)
                df_w = self.extract_way_tags(c, chunk_size, resp, chunk=chunk)
                # self.df_ways = pd.concat([self.df_ways, df_w], ignore_index=True)
                if df_w is not None:
                    df_w1, map_way = self.get_matched_way(df_w, chunk, len(self.df_ways), map_way)
                    self.df_ways = pd.concat([self.df_ways, df_w1], ignore_index=True)
                else:
                    for i in range(len(chunk)):
                        map_way.append(None)
        column_names = ["latitude", "longitude", "lanemerge", "lanesplit"]
        df = self.data.copy()
        df["lanemerge"] = False
        df["lanesplit"] = False
        df["turn_forward"] = False
        if len(self.df_ways) > 0:
            if set(["lanes:forward"]).issubset(self.df_ways) and set(["lanes:backward"]).issubset(
                self.df_ways
            ):
                for i in range(len(self.df_ways)):
                    try:
                        ind = map_way.index(i)
                    except ValueError:
                        ind = None
                    if ind is not None:
                        df.loc[ind, "roadtype"] = self.df_ways["highway"][i]
                        if self.df_ways["direction"][i] == "same":
                            if not pd.isna(self.df_ways["lanes:forward"][i]):
                                df.loc[ind, "lane_forward"] = int(self.df_ways["lanes:forward"][i])
                            if set(["turn:lanes:forward"]).issubset(self.df_ways):
                                if not pd.isna(self.df_ways["turn:lanes:forward"][i]):
                                    if (
                                        "right"
                                        in self.df_ways["turn:lanes:forward"][i].split("|")[-1]
                                    ):
                                        df.loc[ind, "turn_forward"] = True
                        elif self.df_ways["direction"][i] == "opposite":
                            if not pd.isna(self.df_ways["lanes:backward"][i]):
                                df.loc[ind, "lane_forward"] = int(self.df_ways["lanes:backward"][i])
                            if set(["turn:lanes:backward"]).issubset(self.df_ways):
                                if not pd.isna(self.df_ways["turn:lanes:backward"][i]):
                                    if (
                                        "left"
                                        in self.df_ways["turn:lanes:backward"][i].split("|")[-1]
                                    ):
                                        df.loc[ind, "turn_forward"] = True
                prev_ind = 0
                cnt = 0
                for i in range(1, len(df)):
                    if not pd.isna(df.loc[i, "lane_forward"]) and not pd.isna(
                        df.loc[prev_ind, "lane_forward"]
                    ):
                        if cnt < 5:
                            prev_lane = df.loc[prev_ind, "lane_forward"]
                            lane = df.loc[i, "lane_forward"]
                            if lane > prev_lane:
                                if df.loc[i, "turn_forward"]:
                                    df.loc[i, "lanesplit"] = True
                            elif lane < prev_lane:
                                if df.loc[i, "turn_forward"]:
                                    df.loc[i, "lanemerge"] = True
                            prev_ind = i
                        else:
                            prev_ind = i
                        cnt = 0
                    else:
                        prev_ind = i
                        cnt = cnt + 1
        required_df = df[column_names]
        is_lanesplit = True in required_df["lanesplit"].values
        is_lanemerge = True in required_df["lanemerge"].values
        print(f"lane split{is_lanesplit} and lane merge {is_lanemerge}")
        return required_df

    def find_access_blocks(self, lst):
        access_blocks = []
        start = None

        for i, value in enumerate(lst):
            if value == True and start is None:
                start = i
            elif value != True and start is not None:
                access_blocks.append((start, i - 1))
                start = None

        if start is not None:
            access_blocks.append([start, len(lst) - 1])

        return access_blocks

    def get_on_off_ramp(self, data=None, chunk_size=100):
        """Get the location of on ramp and off ramp"""

        if data is not None:
            if "lat" in data:
                data.rename(columns={"lat": "latitude", "lon": "longitude"}, inplace=True)
        column_names = ["latitude", "longitude", "onramp", "offramp"]
        df = data.copy()
        df["onramp"] = False
        df["offramp"] = False
        roadtype = list(df["roadtype"])
        accessramp = list(df["accessramp"])
        access_blocks = self.find_access_blocks(accessramp)
        data_len = len(roadtype)
        if len(access_blocks) > 0:
            for i in range(len(access_blocks)):
                if (access_blocks[i][0] - 5) > 0:
                    r_start = access_blocks[i][0] - 5
                else:
                    r_start = 0
                count = Counter(roadtype[r_start : r_start + 7])
                maj_before = count.most_common(1)[0][0]
                if (access_blocks[i][1] + 5) < data_len:
                    r_end = access_blocks[i][1] + 5
                else:
                    r_end = data_len
                count = Counter(roadtype[r_end - 7 : r_end])
                maj_after = count.most_common(1)[0][0]
                if maj_before != "highway" and maj_after == "highway":
                    df.loc[access_blocks[i][0], "onramp"] = True
                elif maj_before == "highway" and maj_after != "highway":
                    df.loc[access_blocks[i][0], "offramp"] = True
        required_df = df[column_names]

        return required_df

    def built_zebra_crossing_nwr(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            queries.append(
                f"nwr(around:{self.radius},{self.data_c['latitude'][i]},{self.data_c['longitude'][i]})['crossing'='zebra'];"
            )
        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )
        return built_query

    def build_zebra_crossing_nwr_poly(self):

        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            poly_str = self.create_polygon(i)
            queries.append(f'nwr(poly:"{poly_str}")["crossing"="zebra"];')

        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )
        return built_query

    def get_zebra_crossing(self, data=None, chunk_size=None, query_type=None):
        """To find give way locations"""
        if data is not None:
            chunks_df = self.initial_process(data, chunk_size)
            self.df_nodes = pd.DataFrame()
            for chunk in chunks_df:
                self.create_coord_string(chunk)
                if query_type == "poly":
                    # print("using polygon based query")
                    query = self.build_zebra_crossing_nwr_poly()
                else:
                    # print("using radius based query")
                    query = self.built_zebra_crossing_nwr()
                resp = self.execute_query(query)
                # df_n = self.extract_way_node_tags(resp)
                df_n = self.extract_way_node_relation_tags(resp)
                self.df_nodes = pd.concat([self.df_nodes, df_n], ignore_index=True)
        column_names = ["latitude", "longitude", "zebra_crossing"]
        df = self.data.copy()
        df["zebra_crossing"] = False
        if len(self.df_nodes) > 0:
            self.get_mapped_id_node()
            if set(["highway"]).issubset(self.df_nodes):
                for i in range(len(self.mapped_id_nodes)):
                    if self.mapped_id_nodes[i] is not None:
                        ind = self.mapped_id_nodes[i]
                        if self.df_nodes["crossing"][i] == "zebra":
                            df.loc[ind, "zebra_crossing"] = True
        else:
            print("No reponse from server")
        if df["zebra_crossing"].any():
            print("zebra_crossing  present")
        else:
            print("no zebra_crossing present")
        return df[column_names]

    def built_railway_crossing_nwr(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            queries.append(
                f"nwr(around:{self.radius},{self.data_c['latitude'][i]},{self.data_c['longitude'][i]})['railway'='level_crossing'];"
            )
        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )
        return built_query

    def built_railway_crossing_nwr_poly(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            poly_str = self.create_polygon(i)
            queries.append(f'nwr(poly:"{poly_str}")["railway"="level_crossing"];')

        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )
        return built_query

    def get_railway_crossing(self, data=None, chunk_size=None, query_type=None):
        """To find give way locations"""
        if data is not None:
            chunks_df = self.initial_process(data, chunk_size)
            self.df_nodes = pd.DataFrame()
            for chunk in chunks_df:
                self.create_coord_string(chunk)
                if query_type == "poly":
                    # print("using polygon based query")
                    query = self.built_railway_crossing_nwr_poly()
                else:
                    # print("using radius based query")
                    query = self.built_railway_crossing_nwr()
                resp = self.execute_query(query)
                # df_n = self.extract_way_node_tags(resp)
                df_n = self.extract_way_node_relation_tags(resp)
                self.df_nodes = pd.concat([self.df_nodes, df_n], ignore_index=True)
        column_names = ["latitude", "longitude", "railway_crossing"]
        df = self.data.copy()
        df["railway_crossing"] = False
        if len(self.df_nodes) > 0:
            self.get_mapped_id_node()
            if set(["railway"]).issubset(self.df_nodes):
                for i in range(len(self.mapped_id_nodes)):
                    if self.mapped_id_nodes[i] is not None:
                        ind = self.mapped_id_nodes[i]
                        if self.df_nodes["railway"][i] == "level_crossing":
                            df.loc[ind, "railway_crossing"] = True
        else:
            print("No reponse from server")
        if df["railway_crossing"].any():
            print("railway_crossing  present")
        else:
            print("no railway_crossing present")
        return df[column_names]

    def built_hov_lane_nwr(
        self,
    ):
        self.radius = 50
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            lat = self.data_c["latitude"][i]
            lon = self.data_c["longitude"][i]
            queries.append(
                f"nwr(around:{self.radius},{lat},{lon})['hov'];"
                f"nwr(around:{self.radius},{lat},{lon})['hov:lanes'];"
                f"nwr(around:{self.radius},{lat},{lon})['hov:lane:forward'];"
                f"nwr(around:{self.radius},{lat},{lon})['hov:lane:backward'];"
                f"nwr(around:{self.radius},{lat},{lon})['hov:conditional'];"
            )
        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )
        return built_query

    def built_hov_lane_nwr_poly(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            poly_str = self.create_polygon(i)
            queries.append(
                f"nwr(poly:'{poly_str}')['hov'];"
                f"nwr(poly:'{poly_str}')['hov:lanes'];"
                f"nwr(poly:'{poly_str}')['hov:lane:forward'];"
                f"nwr(poly:'{poly_str}')['hov:lane:backward'];"
                f"nwr(poly:'{poly_str}')['hov:conditional'];"
            )

        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )
        return built_query

    def get_hov_lane(self, data=None, chunk_size=None, query_type=None):
        """To find give way locations"""
        if data is not None:
            chunks_df = self.initial_process(data, chunk_size)
            self.df_nodes = pd.DataFrame()
            for chunk in chunks_df:
                self.create_coord_string(chunk)
                if query_type == "poly":
                    # print("using polygon based query")
                    query = self.built_hov_lane_nwr_poly()
                else:
                    # print("using radius based query")
                    query = self.built_hov_lane_nwr()
                resp = self.execute_query(query)
                df_n = self.extract_way_node_relation_tags(resp)
                self.df_nodes = pd.concat([self.df_nodes, df_n], ignore_index=True)
        column_names = ["latitude", "longitude", "hov_lane"]
        df = self.data.copy()
        df["hov_lane"] = False
        if len(self.df_nodes) > 0:
            self.get_mapped_id_node()
            required_columns = [
                "hov",
                "hov:lanes",
                "hov:lanes:forward",
                "hov:lanes:backward",
                "hov:conditional",
            ]
            columns_present = [col for col in required_columns if col in self.df_nodes]
            if columns_present:
                for i in range(len(self.mapped_id_nodes)):
                    if self.mapped_id_nodes[i] is not None:
                        ind = self.mapped_id_nodes[i]
                        hov_conditions = False
                        # Define a function to check split values

                        def check_conditions(column_name):
                            if column_name in columns_present:
                                value = self.df_nodes[column_name].iloc[i]
                                if isinstance(value, str):
                                    # First split by '|', then further split each part by '@'
                                    parts = []
                                    for part in value.split("|"):
                                        parts.extend(part.split("@"))
                                    return any(
                                        part.strip() in ["yes", "designated"] for part in parts
                                    )
                            return False

                        # Check conditions for each relevant column using the function
                        if check_conditions("hov"):
                            hov_conditions = True
                        elif check_conditions("hov:lanes"):
                            hov_conditions = True
                        elif check_conditions("hov:lanes:forward"):
                            hov_conditions = True
                        elif check_conditions("hov:lanes:backward"):
                            hov_conditions = True
                        elif check_conditions("hov:conditional"):
                            hov_conditions = True
                        if hov_conditions:
                            df.loc[ind, "hov_lane"] = True
            else:
                print("No relevant columns are present in df_nodes: No hov lane present")
        else:
            print("No response from server")
        # Check if any hov_lane value is True and print the appropriate message
        if df["hov_lane"].any():
            print("hov_lane present")
        else:
            print("no hov lane present")
        return df[column_names]

    def estimate_lane_position(self, gps_lat, gps_lon, road_lat, road_lon, heading, lane_width=3.5, turn_string=None):
        """
        Estimate lane index from lateral offset between gps point and road center.
        """
        if pd.isna(turn_string) or turn_string == "No":
            return None, None

        lane_turns =turn_string.split("|")
        total_lanes = len(lane_turns)

        if total_lanes == 0:
            return None, None

        # Calculate perpendicular bearing (right of heading)
        perp_bearing = (heading + 90) % 360

        # Compute point on the right side of road by lane_width * total_lanes
        ref_point = Point(road_lat, road_lon)
        right_edge_point = distance(meters=lane_width * total_lanes).destination(ref_point, perp_bearing)

        # Create a vector: road center → right edge
        center = np.array([road_lat, road_lon])
        right = np.array([right_edge_point.latitude, right_edge_point.longitude])
        gps = np.array([gps_lat, gps_lon])

        # Project GPS point on the vector line (use simple ratio)
        total_dist = geodesic(center, right).meters
        gps_dist = geodesic(center, gps).meters

        if total_dist == 0:
            lane_idx = total_lanes // 2
        else:
            ratio = gps_dist / total_dist
            lane_idx = int(ratio * total_lanes)
            lane_idx = min(max(lane_idx, 0), total_lanes - 1)

        return lane_idx+1, lane_turns[lane_idx]

    def classify_heading_turn_old(self, prev_heading, next_heading, threshold=10):
        delta = (next_heading - prev_heading + 360) % 360
        if delta > 180:
            delta -= 360

        if -threshold <= delta <= threshold:
            return "through"
        elif delta > threshold:
            return "right"
        else:
            return "left"

    def get_turn_from_heading_and_tag(self, prev_heading, next_heading, turn_string, threshold=10):
        if pd.isna(turn_string) or turn_string == "No":
            return None

        heading_turn = self.classify_heading_turn(prev_heading, next_heading, threshold)

        normalized = turn_string.split("|")
        # 1. Check for partial match
        for value in normalized:
            if heading_turn in value:
                return heading_turn
        # 2. Check for 'through' with 'none' or empty string
        if heading_turn == 'through' and any(val in ('none', '') for val in normalized):
            return 'through'
        # 3. Default case
        return None
        # if heading_turn in normalized:
        #     return heading_turn
        # else:
        #     return None

    def built_turn_left_right_nwr(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            lat = self.data_c["latitude"][i]
            lon = self.data_c["longitude"][i]
            queries.append(
                # f"nwr(around:{self.radius},{lat},{lon})['turn'];"
                # f"nwr(around:{self.radius},{lat},{lon})['turn:forward'];"
                # f"nwr(around:{self.radius},{lat},{lon})['turn:backward'];"
                # f"nwr(around:{self.radius},{lat},{lon})['turn:both_ways'];"
                f"nwr(around:{self.radius},{lat},{lon})['turn:lanes'];"
                # f"nwr(around:{self.radius},{lat},{lon})['turn:lanes:forward'];"
                # f"nwr(around:{self.radius},{lat},{lon})['turn:lanes:backward'];"
                # f"nwr(around:{self.radius},{lat},{lon})['turn:lanes:both_ways'];"
            )
        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )
        return built_query

    def built_turn_left_right_nwr_poly(
        self,
    ):
        self.data_c.drop_duplicates(
            subset=["latitude", "longitude"], inplace=True, ignore_index=True
        )
        queries = []
        for i in range(len(self.data_c)):
            poly_str = self.create_polygon(i)
            queries.append(
                # f"nwr(poly:'{poly_str}')['turn'];"
                # f"nwr(poly:'{poly_str}')['turn:forward'];"
                # f"nwr(poly:'{poly_str}')['turn:backward'];"
                # f"nwr(poly:'{poly_str}')['turn:both_ways'];"
                f"nwr(poly:'{poly_str}')['turn:lanes'];"
                # f"nwr(poly:'{poly_str}')['turn:lanes:forward'];"
                # f"nwr(poly:'{poly_str}')['turn:lanes:backward'];"
                # f"nwr(poly:'{poly_str}')['turn:lanes:both_ways'];"
            )

        built_query = (
            "[out:json][timeout:4000];(" + "".join(queries) + ");out body; >; out skel qt;"
        )
        return built_query

    def get_turn_left_right_prev_next_heading(self, data=None, chunk_size=None, query_type=None):
        if data is not None:
            chunks_df = self.initial_process(data, chunk_size)
            self.df_nodes = pd.DataFrame()
            for chunk in chunks_df:
                self.create_coord_string(chunk)
                if query_type == "poly":
                    print("using polygon based query")
                    query = self.built_turn_left_right_nwr_poly()
                else:
                    print("using radius based query")
                    query = self.built_turn_left_right_nwr()
                resp = self.execute_query(query)
                df_n = self.extract_way_node_relation_tags(resp)
                self.df_nodes = pd.concat([self.df_nodes, df_n], ignore_index=True)
        column_names = ["latitude", "longitude", "turn", "turn_direction"]
        df = self.data.copy()
        df["turn"] = np.nan
        df["turn"] = df["turn"].astype(object)

        if len(self.df_nodes) > 0:
            self.get_mapped_id_node()
            if set(["turn"]).issubset(self.df_nodes) or set(["turn:lanes"]).issubset(self.df_nodes):
                for i in range(len(self.mapped_id_nodes)):
                    if self.mapped_id_nodes[i] is not None:
                        ind = self.mapped_id_nodes[i]
                        df.loc[ind, "turn"] = self.df_nodes.loc[i, "turn:lanes" if "turn:lanes" in self.df_nodes.columns else "turn"]
        else:
            print("No response from server")

        # --- Add heading-based turn_direction ---
        df["turn_direction"] = np.nan
        df["turn"] = df["turn"].astype(object)

        # for i in range(1, len(df) - 1):
        #     try:
        #         prev_lat, prev_lon = df.loc[i - 1, ["latitude", "longitude"]]
        #         curr_lat, curr_lon = df.loc[i, ["latitude", "longitude"]]
        #         next_lat, next_lon = df.loc[i + 1, ["latitude", "longitude"]]

        #         prev_heading = self.get_bearing_angle(prev_lat, prev_lon, curr_lat, curr_lon)
        #         next_heading = self.get_bearing_angle(curr_lat, curr_lon, next_lat, next_lon)

        #         turn_string = df.loc[i, "turn"]
        #         turn_direction = self.get_turn_from_heading_and_tag(prev_heading, next_heading, turn_string)

        #         df.at[i, "turn_direction"] = turn_direction
        #     except Exception as e:
        #         print(f"Error processing turn_direction at index {i}: {e}")
        #         continue
        for i in range(len(df)):
            try:
                curr_lat, curr_lon = df.loc[i, ["latitude", "longitude"]]
                turn_string = df.loc[i, "turn"]

                if i == 0 and i + 1 < len(df):
                    # Only use next point for heading
                    next_lat, next_lon = df.loc[i + 1, ["latitude", "longitude"]]
                    next_heading = self.get_bearing_angle(curr_lat, curr_lon, next_lat, next_lon)
                    prev_heading = next_heading # fallback to same heading
                elif i == len(df) - 1 and i - 1 >= 0:
                    # Only use previous point for heading
                    prev_lat, prev_lon = df.loc[i - 1, ["latitude", "longitude"]]
                    prev_heading = self.get_bearing_angle(prev_lat, prev_lon, curr_lat, curr_lon)
                    next_heading = prev_heading # fallback to same heading
                elif 0 < i < len(df) - 1:
                    prev_lat, prev_lon = df.loc[i - 1, ["latitude", "longitude"]]
                    next_lat, next_lon = df.loc[i + 1, ["latitude", "longitude"]]
                    prev_heading = self.get_bearing_angle(prev_lat, prev_lon, curr_lat, curr_lon)
                    next_heading = self.get_bearing_angle(curr_lat, curr_lon, next_lat, next_lon)
                else:
                    # single point or invalid
                    continue

                turn_dir = self.get_turn_from_heading_and_tag(prev_heading, next_heading, turn_string)
                df.at[i, "turn_direction"] = turn_dir

            except Exception as e:
                print(f"Error processing turn_direction at index {i}: {e}")
                continue

        if df["turn_direction"].any():
            print("turn present")
        else:
            print("no turn present")
        return df[column_names]

    def classify_heading_direction_old(self, heading, left_thresh=135, right_thresh=45):
        """
        Classify movement direction from a heading angle:
        - 0° = North, 90° = East, 180° = South, 270° = West
        Returns: 'left', 'right', 'through'
        """
        heading = heading % 360
        if right_thresh < heading <= 135:
            return "right"
        elif 225 <= heading < (360 - left_thresh):
            return "left"
        else:
            return "through"

    def classify_heading_direction_one_threshold(self, heading, threshold=10):
        """
        Classifies movement direction based on absolute heading from forward direction.
        0° (or 360°) is considered straight/forward.

        Parameters:
            heading: float - absolute heading angle
            threshold: int - degrees within which to consider movement 'through'

        Returns: 'left', 'right', or 'through'
        """
        heading = heading % 360

        # Calculate how far off the heading is from 0° (forward)
        delta = (heading - 0 + 360) % 360
        if delta > 180:
            delta -= 360 # Normalize to [-180, 180]

        if -threshold <= delta <= threshold:
            return "through"
        elif delta > threshold:
            return "right"
        else:
            return "left"

    def classify_heading_direction(self, heading, turn_thresh=20):
        """
        Classify direction based on a single heading angle.
        Uses balanced zones for better turn detection.

        Parameters:
        -----------
        heading : float
            Heading angle in degrees (0-360)
        turn_thresh : float
            Threshold in degrees around cardinal directions for turn classification (default: 30)
            Smaller values = stricter turn detection, Larger values = more sensitive turn detection

        Returns:
        --------
        str
            'through', 'left', or 'right'
        """
        # Normalize heading to 0-360 range
        heading = heading % 360

        # Define turn zones around cardinal directions
        # Right turn zone: around East (90°) ± turn_thresh
        right_turn_min = 90 - turn_thresh  # 60°
        right_turn_max = 90 + turn_thresh  # 120°

        # Left turn zone: around West (270°) ± turn_thresh
        left_turn_min = 270 - turn_thresh  # 240°
        left_turn_max = 270 + turn_thresh  # 300°

        # Classify based on zones
        if right_turn_min <= heading <= right_turn_max:
            return "right"
        elif left_turn_min <= heading <= left_turn_max:
            return "left"
        else:
            return "through"

    def classify_heading_direction_alternative(self, heading, road_direction=None):
        """
        Alternative method: Classify based on deviation from expected road direction.
        If you know the general road direction, this can be more accurate.

        Parameters:
        -----------
        heading : float
            Current heading angle in degrees (0-360)
        road_direction : float, optional
            Expected road direction in degrees. If None, assumes North (0°)

        Returns:
        --------
        str
            'through', 'left', or 'right'
        """
        if road_direction is None:
            road_direction = 0  # Assume North if not specified

        # Calculate deviation from expected road direction
        deviation = self.calculate_heading_difference(road_direction, heading)

        # Classify based on deviation
        if abs(deviation) <= 30:  # Within 30 degrees is "through"
            return "through"
        elif deviation > 0:
            return "right"
        else:
            return "left"

    def calculate_heading_difference(self, heading1, heading2):
        """
        Calculate the angular difference between two headings.
        Returns a value between -180 and +180 degrees.
        """
        # Normalize headings to 0-360 range
        heading1 = heading1 % 360
        heading2 = heading2 % 360

        # Calculate raw difference
        diff = heading2 - heading1

        # Normalize to -180 to +180 range
        if diff > 180:
            diff -= 360
        elif diff < -180:
            diff += 360

        return diff

    def flip_turn_direction(self, direction):
        if direction == "left":
            return "right"
        elif direction == "right":
            return "left"
        return direction  # through

    def extract_all_turns(self, turn_string):
        if pd.isna(turn_string) or turn_string == "No":
            return []
        all_lanes = turn_string.strip().lower().split("|")
        allowed = set()
        for lane in all_lanes:
            turns = [t.strip() for t in lane.split(";")]
            # Replace 'none' or '' with 'through'
            normalized_turns = ["through" if t in ("none", "") else t for t in turns]
            allowed.update(normalized_turns)
        return sorted(allowed)

    def get_turn_left_right(self, data=None, chunk_size=None, query_type=None):
        if data is not None:
            chunks_df = self.initial_process(data, chunk_size)
            self.df_nodes = pd.DataFrame()

            for chunk in chunks_df:
                self.create_coord_string(chunk)

                if query_type == "poly":
                    print("Using polygon-based query")
                    query = self.built_turn_left_right_nwr_poly()
                else:
                    print("Using radius-based query")
                    query = self.built_turn_left_right_nwr()

                resp = self.execute_query(query)
                df_n = self.extract_way_node_relation_tags(resp)
                self.df_nodes = pd.concat([self.df_nodes, df_n], ignore_index=True)
        column_names = ["latitude", "longitude", "turn", "turn_direction", "optional_turns", "turn_flag"]
        df = self.data.copy()
        df["turn"] = np.nan
        df["turn"] = df["turn"].astype(object)

        if len(self.df_nodes) > 0:
            self.get_mapped_id_node()
            if set(["turn"]).issubset(self.df_nodes) or set(["turn:lanes"]).issubset(self.df_nodes):
                for i in range(len(self.mapped_id_nodes)):
                    if self.mapped_id_nodes[i] is not None:
                        ind = self.mapped_id_nodes[i]
                        df.loc[ind, "turn"] = self.df_nodes.loc[i, "turn:lanes" if "turn:lanes" in self.df_nodes.columns else "turn"]
        else:
            print("No response from server")

        df["turn_direction"] = np.nan
        df["turn_direction"] = df["turn_direction"].astype(object)
        df["optional_turns"] = np.nan
        df["optional_turns"] = df["optional_turns"].astype(object)
        df["turn_flag"] = "none"

        cumulative_distance = df["cumulative_distance"].values
        latitudes = df["latitude"].values
        longitudes = df["longitude"].values

        for i in range(len(df)):
            try:
                turn_string = df.loc[i, "turn"]
                has_turn = pd.notna(turn_string) and turn_string != "No"
                if not has_turn:
                    continue

                curr_lat = latitudes[i]
                curr_lon = longitudes[i]
                cumulative = 0
                forward_idx = None

                for j in range(i + 1, len(df)):
                    cumulative += cumulative_distance[j]
                    if cumulative >= 40:
                        forward_idx = j
                        break

                reverse = False
                if forward_idx:
                    ref_lat = latitudes[forward_idx]
                    ref_lon = longitudes[forward_idx]
                    heading = self.get_bearing_angle(curr_lat, curr_lon, ref_lat, ref_lon)
                else:
                    cumulative = 0
                    backward_idx = None
                    for j in range(i - 1, -1, -1):
                        cumulative += cumulative_distance[j]
                        if cumulative >= 40:
                            backward_idx = j
                            break
                    if backward_idx is not None:
                        ref_lat = latitudes[backward_idx]
                        ref_lon = longitudes[backward_idx]
                        heading = self.get_bearing_angle(ref_lat, ref_lon, curr_lat, curr_lon)
                        reverse = True
                    else:
                        continue  # Not enough data for heading

                # turn_dir = self.classify_heading_direction_alternative(heading)
                turn_dir = self.classify_heading_direction(heading)
                if reverse:
                    turn_dir = self.flip_turn_direction(turn_dir)

                allowed_turns = self.extract_all_turns(turn_string)
                df.at[i, "optional_turns"] = ";".join(allowed_turns)

                if turn_dir in allowed_turns:
                    df.at[i, "turn_direction"] = turn_dir
                    df.at[i, "turn_flag"] = "valid"
                else:
                    df.at[i, "turn_direction"] = turn_dir
                    df.at[i, "turn_flag"] = "invalid"

            except Exception as e:
                print(f"Error at index {i}: {e}")
                continue

        # Fallback propagation - handle edge cases including last row
        for i in range(1, len(df)):
            # Check if current row has missing turn data and previous row has valid turn_flag
            current_turn_missing = pd.isna(df.loc[i, "turn"]) or df.loc[i, "turn"] is None
            prev_valid = df.loc[i - 1, "turn_flag"] == "valid"

            # For rows that are not the last row, also check next row
            if i < len(df) - 1:
                next_valid = df.loc[i + 1, "turn_flag"] == "valid"
                condition = current_turn_missing and prev_valid and next_valid
            else:
                # For the last row, only check previous row
                condition = current_turn_missing and prev_valid

            if condition:
                df.at[i, "turn_direction"] = df.loc[i - 1, "turn_direction"]
                df.at[i, "turn_flag"] = "fallback"
                df.at[i, "optional_turns"] = df.loc[i - 1, "optional_turns"]

        return df[column_names]


    def create_map(self, downsampledDf, output_column, output_html):
        # === Generate polygons & markers ===
        polygon_js_blocks = []
        marker_js_blocks = []

        for i in range(len(downsampledDf)):
            output_col = downsampledDf.loc[i, output_column]
            no_of_lanes = downsampledDf.loc[i, "lane"]
            left_side = right_side = 4 if pd.isna(no_of_lanes) or no_of_lanes == 1 else no_of_lanes * 4

            if len(downsampledDf) == 1:
                lat_ref, lon_ref = downsampledDf.loc[i, ["lat", "lon"]]
            elif i == len(downsampledDf) - 1:
                lat_ref, lon_ref = downsampledDf.loc[i - 1, ["lat", "lon"]]
            else:
                lat_ref, lon_ref = downsampledDf.loc[i + 1, ["lat", "lon"]]

            lat, lon = downsampledDf.loc[i, ["lat", "lon"]]
            heading = self.get_bearing_angle(lat, lon, lat_ref, lon_ref)

            left_bearing = (heading - 90) % 360
            right_bearing = (heading + 90) % 360

            point_a = Point(lat, lon)
            point_b = distance(meters=left_side).destination(point_a, left_bearing)
            point_c = distance(meters=right_side).destination(point_a, right_bearing)

            if i + 1 < len(downsampledDf):
                cumulative_distance = downsampledDf.loc[i + 1, "cumulative_distance"]
            else:
                cumulative_distance = downsampledDf.loc[i, "cumulative_distance"]

            cumulative_distance = max(cumulative_distance, 0)

            point_d = distance(meters=cumulative_distance).destination(point_b, heading)
            point_e = distance(meters=cumulative_distance).destination(point_c, heading)

            coords = [
                [lat, lon],
                [point_b.latitude, point_b.longitude],
                [point_d.latitude, point_d.longitude],
                [point_e.latitude, point_e.longitude],
                [point_c.latitude, point_c.longitude],
                [lat, lon],
            ]

            coords_js = "[" + ", ".join(f"[{lat:.6f}, {lon:.6f}]" for lat, lon in coords) + "]"
            polygon_js_blocks.append(f"L.polygon({coords_js}, {{color: 'red'}}).addTo(map);")

            # popup_content = (
            #     f"<b>Point {i}</b><br>"
            #     f"Lat: {lat:.6f}<br>"
            #     f"Lon: {lon:.6f}<br>"
            #     f"Left Side: {left_side} m<br>"
            #     f"Right Side: {right_side} m<br>"
            #     f"Distance: {cumulative_distance:.2f} m"
            # )

            google_maps_url = (
                f"https://www.google.com/maps/@?api=1&map_action=pano"
                f"&viewpoint={lat:.6f},{lon:.6f}&heading={heading:.2f}&pitch=0&fov=80"
            )

            popup_content = (
                f"<b>Point {i}</b><br>"
                f"Lat: {lat:.6f}<br>"
                f"Lon: {lon:.6f}<br>"
                f"Heading: {heading:.2f}<br>"
                f"Left Side: {left_side} m<br>"
                f"Right Side: {right_side} m<br>"
                f"Distance: {cumulative_distance:.2f} m<br>"
                f"{output_column}: {output_col}<br>"
                f"Turn: {downsampledDf.loc[i, 'turn']}<br>"
                # f"Output Column: {downsampledDf.loc[i, 'lane_turn']}<br>"
                f"<a href='{google_maps_url}' target='_blank'>Street View</a>"

            )

            marker_js_blocks.append(
                f"L.marker([{lat:.6f}, {lon:.6f}]).addTo(map).bindPopup(`{popup_content}`);"
            )

        polygons_js = "\n".join(polygon_js_blocks)
        markers_js = "\n".join(marker_js_blocks)

        # === HTML Output ===
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>GPS Rectangles Map</title>
            <meta charset="utf-8" />
            <style> #map {{ height: 100vh; width: 100%; }} </style>
            <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
            <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
        </head>
        <body>
        <div id="map"></div>
        <script>
            var map = L.map('map').setView([{downsampledDf.iloc[0]['lat']}, {downsampledDf.iloc[0]['lon']}], 18);
            L.tileLayer('https://{{s}}.tile.openstreetmap.org/{{z}}/{{x}}/{{y}}.png', {{
                maxZoom: 22,
                attribution: '&copy; OpenStreetMap contributors'
            }}).addTo(map);

            {polygons_js}
            {markers_js}
        </script>
        </body>
        </html>
        """

        with open(output_html, "w") as f:
            f.write(html_template)

        print(f"Map saved to: {output_html}")

if __name__ == "__main__":

    # # # ***************************
    # # # Narrow road co ordinates - tag -> traffic_calming = chicane or choker
    # df_trip = pd.read_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/narrow_0e57fbb9.csv"
    # )
    # osm = CustomMapObjects()
    # df_trip_downsampled = osm.downsampleData(df_trip)
    # narrow_road_df = osm.get_narrow_road(
    #     data=df_trip_downsampled[
    #         ["lat", "lon", "lane", "gpsdistance", "cumulative_distance"]
    #     ].copy(),
    #     chunk_size=30,
    #     query_type="poly",
    # )
    # # narrow_road_df = osm.get_narrow_road(data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30)
    # narrow_road_df.to_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/narrow_0e57fbb9_poly_with_meta_out_new.csv",
    #     index=False,
    # )
    # print(narrow_road_df)
    # print("done")

    # # # ***************************
    # Village co ordinates - tag -> place = village
    # df_trip = pd.read_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/germany_village_bdec6a91.csv"
    # )
    # # df_trip = pd.read_parquet(
    # #    "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/bdec6a91-cbef-4151-afd6-0190a2225995.parquet"
    # # )
    # osm = CustomMapObjects()
    # village_df = osm.get_village(data=df_trip[["lat", "lon", "roadtype"]].copy(), chunk_size=30)
    # village_df.to_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/bdec6a91-cbef-4151-afd6-0190a2225995_out.parquet",
    #     index=False,
    # )
    # print(village_df)
    # print("done")

    # # # ****************************
    # # #  # Zebra crossing co ordinates - tag -> crossing = zebra
    # df_trip = pd.read_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/14dc13bf_zebra.csv"
    # )
    # osm = CustomMapObjects()
    # df_trip_downsampled = osm.downsampleData(df_trip)
    # zebra_crossing_df = osm.get_zebra_crossing(
    # data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30, query_type="poly"
    # )
    # # zebra_crossing_df = osm.get_zebra_crossing(data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30)
    # zebra_crossing_df.to_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/14dc13bf_zebra_poly_with_meta_out_new.csv",
    #     index=False,
    # )
    # print(zebra_crossing_df)
    # print("done")

    # # # ****************************
    # # # Railway crossing co ordinates - tag -> railway=level_crossing
    # df_trip = pd.read_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/railway_ba591882.csv"
    # )
    # osm = CustomMapObjects()
    # df_trip_downsampled = osm.downsampleData(df_trip)
    # railway_crossing_df = osm.get_railway_crossing(
    # data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30, query_type="poly"
    # )
    # # railway_crossing_df = osm.get_railway_crossing(
    # data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30
    # )
    # railway_crossing_df.to_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/railway_ba591882_poly_with_meta_out_new.csv",
    #     index=False,
    # )
    # print(railway_crossing_df)
    # print("done")

    # # # ****************************
    # # # HOV lane (high-occupancy vehicle lane) co ordinates
    # # # tag -> hov, hov:lanes, hov:lanes:forward, hov:lanes:backward, hov:conditional = yes or designated
    # df_trip = pd.read_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/hov_d4f9a594.csv"
    # )
    # osm = CustomMapObjects()
    # df_trip_downsampled = osm.downsampleData(df_trip)
    # hov_df = osm.get_hov_lane(data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30, query_type="poly")
    # # hov_df = osm.get_hov_lane(data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30)
    # hov_df.to_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/hov_d4f9a594_poly_with_meta_out_new.csv",
    #     index=False,
    # )
    # print(hov_df)
    # print("done")

    # # ****************************
    # # # Padestrian crossing & traffic light : Padestrian crossing tag -> highway = crossing, traffic light tag -> crossing=traffic_signals
    # df_trip = pd.read_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/padestrain_traffic_699d8f8d.csv"
    # )
    # osm = CustomMapObjects()
    # df_trip_downsampled = osm.downsampleData(df_trip)
    # pad_df = osm.get_ts_padestrian_crossing(
    #     data=df_trip_downsampled[
    #         ["lat", "lon", "lane", "gpsdistance", "cumulative_distance"]
    #     ].copy(),
    #     chunk_size=30,
    #     query_type="poly",
    # )
    # # pad_df = osm.get_ts_padestrian_crossing(data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30)
    # pad_df.to_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/padestrain_traffic_699d8f8d_poly_with_meta_out_new.csv",
    #     index=False,
    # )
    # print(pad_df)
    # print("done")

    # # # ****************************
    # # # stop sign & 4way stop : stop sign tag -> highway = crossing, 4way stop tag -> stop == all
    # df_trip = pd.read_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/stop_4way_988f4eda.csv"
    # )
    # osm = CustomMapObjects()
    # df_trip_downsampled = osm.downsampleData(df_trip)
    # stop_df = osm.get_stop_sign(
    # data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30, query_type="poly"
    # )
    # # stop_df = osm.get_stop_sign(data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30)
    # stop_df.to_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/stop_4way_988f4eda_poly_with_meta_out_new.csv",
    #     index=False,
    # )
    # print(stop_df)
    # print("done")

    # # # ****************************
    # # # give way sign tag -> highway = give_way
    # df_trip = pd.read_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/give_way_e0d81310.csv"
    # )
    # osm = CustomMapObjects()
    # df_trip_downsampled = osm.downsampleData(df_trip)
    # # give_df = osm.get_give_way(
    # data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30, query_type="poly"
    # )
    # give_df = osm.get_give_way(data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30)
    # give_df.to_csv(
    #     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/give_way_e0d81310_poly_with_meta_out_new.csv",
    #     index=False,
    # )
    # print(give_df)
    # print("done")

    # # ****************************
    # # Turn left and right tag ->   turn; turn:forward; turn:backward; turn:lanes:both_ways
    # # turn:lanes; turn:lanes:forward; turn:lanes:backward; turn:lanes:both_ways


    # df_trip = pd.DataFrame(columns=["lat", "lon","lane", "cumulative_distance"])
    # df_trip['lat'] = [48.523146]
    # df_trip['lon'] = [9.052179]
    # df_trip['lane'] = [1]
    # df_trip['cumulative_distance'] = [11]
    # osm = CustomMapObjects()
    # turn_df = osm.get_turn_left_right(
    #     data=df_trip[
    #         ["lat", "lon", "lane", "cumulative_distance"]
    #     ].copy(),
    #     chunk_size=30,
    #     query_type="poly",
    # )

    df_trip = pd.read_csv(
        "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/turn-86d8f771.csv"
    )
    osm = CustomMapObjects()
    df_trip_downsampled = osm.downsampleData(df_trip)
    turn_df = osm.get_turn_left_right(data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30, query_type="poly")
    # turn_df = osm.get_turn_left_right(data=df_trip_downsampled[["lat", "lon", "lane","gpsdistance", "cumulative_distance" ]].copy(), chunk_size=30)
    turn_df.to_csv(
        "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/turn-86d8f771_poly_1_turn_lane_v8_t20.csv",
        index=False,
    )
    print(turn_df)
    print("done")
    # # ********** create html map ***************
    output_html = "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/turn-86d8f771_map_v6.html"
    turn_df = turn_df.rename(columns={
    'latitude': 'lat',
    'longitude': 'lon'
    })
    merged_df = turn_df.merge(
    df_trip_downsampled[['lat', 'lon', 'lane', 'gpsdistance', 'cumulative_distance']],
    on=['lat', 'lon'],
    how='left',
    sort=True
    )
    osm.create_map(merged_df, "turn_direction", output_html)

    # # ****************************

    # import awswrangler as wr
    # df_trip = pd.DataFrame(columns=["lat", "lon"])
    # df_trip["lat"] = [39.2003692]
    # df_trip["lon"] = [-106.8473625]
    # osm = CustomMapObjects()
    # # lsm = osm.get_lanesplit_merge(data=df_trip[["lat", "lon"]].copy(), chunk_size=10)
    # database = "realm_infra_search"
    # table = "latest_common_encostatic"
    # workgroup = "orion-alfwv1-prod-athena-workgroup"
    # sessionid = "35f15696-1e35-41c9-a802-01931120de3d"
    # query = f"""
    #             SELECT sessionid, lat, lon, timestamp, roadtype, lane,roadsurface, accessramp
    #             FROM {database}.{table}
    #             WHERE sessionid = '{sessionid}'
    #             """
    # file_gen = False
    # if file_gen:
    #     df = wr.athena.read_sql_query(query, database=database, workgroup=workgroup)
    #     df.to_parquet("api/enrichment/compass/test_parquet.parquet", engine="fastparquet")
    # else:
    #     st = time.time()
    #     df_trip = pd.read_parquet("test_parquet.parquet", engine="fastparquet")
    #     # df_trip = df_trip[3000:4000]
    #     df_trip = df_trip.sort_values(by="timestamp").reset_index(drop=True)

    #     osm = CustomMapObjects()
    #     chunk_size = 30
    #     # on_off_ramp = osm.get_on_off_ramp(data=df_trip[["lat", "lon","accessramp","roadtype"]].copy(), chunk_size=chunk_size)
    #     lsm = osm.get_lanesplit_merge(
    #         data=df_trip[["lat", "lon", "accessramp", "roadtype"]].copy(), chunk_size=chunk_size
    #     )
    #     print("time consumed: ", (time.time() - st))
